# 🧪 Comprehensive Test Suite - Go Docker Platform

This document contains all tests to verify the complete functionality of the Go Docker Platform CRM system.

## 📋 Test Overview

### Test Categories
1. **Frontend Build & Development Tests**
2. **Backend Service Health Tests**
3. **Database Connection Tests**
4. **API Integration Tests**
5. **Authentication & Authorization Tests**
6. **End-to-End User Flow Tests**
7. **Performance & Load Tests**

---

## 🎯 1. Frontend Build & Development Tests

### Test 1.1: Build Verification
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Run type checking
npm run type-check

# Build for production
npm run build

# Verify build output
node test-build.cjs
```

**Expected Results:**
- ✅ All dependencies installed successfully
- ✅ TypeScript compilation passes
- ✅ Production build completes without errors
- ✅ All required files present in dist/ folder

### Test 1.2: Development Server
```bash
# Start development server
npm run dev

# Expected output:
# VITE v4.5.14  ready in XXX ms
# ➜  Local:   http://localhost:3000/
# ➜  Network: use --host to expose
```

**Expected Results:**
- ✅ Development server starts on port 3000
- ✅ Hot reload functionality works
- ✅ No console errors in browser

### Test 1.3: Linting & Code Quality
```bash
# Run ESLint
npm run lint

# Expected: No critical errors, only warnings allowed
```

**Expected Results:**
- ✅ No blocking ESLint errors
- ✅ Code follows established patterns

---

## 🔧 2. Backend Service Health Tests

### Test 2.1: API Gateway Health
```bash
curl -X GET "https://crm-api-gateway.onrender.com/health" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T10:30:00Z",
  "services": {
    "auth-service": "healthy",
    "admin-service": "healthy", 
    "staff-service": "healthy",
    "payment-service": "healthy",
    "notification-service": "healthy"
  }
}
```

### Test 2.2: Individual Service Health Checks
```bash
# Auth Service
curl https://crm-auth-service.onrender.com/health

# Admin Service  
curl https://crm-admin-service.onrender.com/health

# Staff Service
curl https://crm-staff-service.onrender.com/health

# Payment Service
curl https://crm-payment-service.onrender.com/health

# Notification Service
curl https://crm-notification-service.onrender.com/health
```

**Expected Results:**
- ✅ All services return 200 OK
- ✅ Response time < 2 seconds
- ✅ Status: "healthy" for all services

---

## 🗄️ 3. Database Connection Tests

### Test 3.1: Database Connectivity
```bash
# Run database connection test
cd tests
go run test-db-connection.go
```

**Expected Results:**
- ✅ All 5 databases connect successfully
- ✅ Connection pool established
- ✅ Basic CRUD operations work

### Test 3.2: Database Schema Validation
```bash
# Check if all required tables exist
cd scripts
./validate-schemas.sh
```

**Expected Results:**
- ✅ All required tables present
- ✅ Foreign key constraints valid
- ✅ Indexes properly created

---

## 🔐 4. Authentication & Authorization Tests

### Test 4.1: User Registration
```bash
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "first_name": "Test",
    "last_name": "User",
    "role": "RECEPTION"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "RECEPTION"
    },
    "token": "jwt_token_here"
  }
}
```

### Test 4.2: User Login
```bash
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "ADMIN"
    },
    "token": "jwt_token_here",
    "refresh_token": "refresh_token_here"
  }
}
```

### Test 4.3: Protected Route Access
```bash
# Get JWT token from login response
TOKEN="your_jwt_token_here"

curl -X GET "https://crm-api-gateway.onrender.com/api/v1/admin/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Results:**
- ✅ Valid token allows access
- ✅ Invalid token returns 401
- ✅ Role-based access enforced

---

## 🎭 5. End-to-End User Flow Tests

### Test 5.1: Admin User Flow
1. **Login as Admin**
   - Navigate to http://localhost:3000
   - Login with: <EMAIL> / admin123
   - Verify dashboard loads

2. **User Management**
   - Navigate to Users section
   - Create new user
   - Edit user details
   - Verify user list updates

3. **Analytics Dashboard**
   - View analytics dashboard
   - Verify charts load
   - Check data accuracy

### Test 5.2: Staff User Flow
1. **Login as Staff**
   - Login with reception credentials
   - Verify role-appropriate dashboard

2. **Lead Management**
   - Create new lead
   - Convert lead to student
   - Verify lead status updates

3. **Student Management**
   - View student list
   - Add new student
   - Update student information

### Test 5.3: Payment Processing Flow
1. **Payment Creation**
   - Navigate to payments
   - Create new payment
   - Select payment method

2. **Payment Verification**
   - Verify payment status
   - Check transaction logs
   - Confirm database updates

---

## 📊 6. API Integration Tests

### Test 6.1: CRUD Operations
```bash
# Create Student
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/staff/students" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+998901234567"
  }'

# Get Students
curl -X GET "https://crm-api-gateway.onrender.com/api/v1/staff/students" \
  -H "Authorization: Bearer $TOKEN"

# Update Student
curl -X PUT "https://crm-api-gateway.onrender.com/api/v1/staff/students/{id}" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John Updated"
  }'

# Delete Student
curl -X DELETE "https://crm-api-gateway.onrender.com/api/v1/staff/students/{id}" \
  -H "Authorization: Bearer $TOKEN"
```

### Test 6.2: Error Handling
```bash
# Test invalid data
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/staff/students" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "invalid_field": "value"
  }'
```

**Expected Response:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Required fields missing",
    "details": ["first_name is required", "last_name is required"]
  }
}
```

---

## ⚡ 7. Performance & Load Tests

### Test 7.1: Response Time Test
```bash
# Test API response times
for i in {1..10}; do
  curl -w "@curl-format.txt" -o /dev/null -s \
    "https://crm-api-gateway.onrender.com/health"
done
```

**Expected Results:**
- ✅ Average response time < 500ms
- ✅ 95th percentile < 1000ms
- ✅ No timeouts or errors

### Test 7.2: Concurrent User Test
```bash
# Simulate 50 concurrent users
ab -n 1000 -c 50 https://crm-api-gateway.onrender.com/health
```

**Expected Results:**
- ✅ All requests successful
- ✅ No 5xx errors
- ✅ Consistent response times

---

## 🔍 8. Security Tests

### Test 8.1: SQL Injection Protection
```bash
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>'\'' OR 1=1--",
    "password": "anything"
  }'
```

**Expected Results:**
- ✅ Request rejected or returns authentication error
- ✅ No database errors in logs

### Test 8.2: XSS Protection
```bash
curl -X POST "https://crm-api-gateway.onrender.com/api/v1/staff/students" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "<script>alert(\"xss\")</script>",
    "last_name": "Test"
  }'
```

**Expected Results:**
- ✅ Script tags sanitized or rejected
- ✅ No script execution in frontend

---

## ✅ Test Execution Checklist

### Pre-Test Setup
- [ ] All services deployed and running
- [ ] Database connections established
- [ ] Environment variables configured
- [ ] Test data prepared

### Frontend Tests
- [ ] Build verification passed
- [ ] Development server starts
- [ ] Linting passes
- [ ] TypeScript compilation successful

### Backend Tests  
- [ ] All service health checks pass
- [ ] Database connections verified
- [ ] API endpoints responding
- [ ] Authentication working

### Integration Tests
- [ ] End-to-end user flows complete
- [ ] CRUD operations working
- [ ] Error handling proper
- [ ] Security measures effective

### Performance Tests
- [ ] Response times acceptable
- [ ] Load testing passed
- [ ] No memory leaks detected
- [ ] Concurrent user handling verified

---

## 🚨 Troubleshooting

### Common Issues
1. **Service Timeout**: Check Render service logs
2. **Database Connection**: Verify connection strings
3. **Authentication Errors**: Check JWT token validity
4. **CORS Issues**: Verify frontend/backend URLs

### Debug Commands
```bash
# Check service logs
curl https://crm-api-gateway.onrender.com/debug/logs

# Verify environment
curl https://crm-api-gateway.onrender.com/debug/env

# Database status
curl https://crm-api-gateway.onrender.com/debug/db
```

---

## 📈 Success Criteria

### All Tests Must Pass
- ✅ Frontend builds and runs without errors
- ✅ All backend services healthy
- ✅ Database connections stable
- ✅ Authentication/authorization working
- ✅ CRUD operations functional
- ✅ Performance within acceptable limits
- ✅ Security measures effective

### Ready for Production
- ✅ All tests green
- ✅ Documentation complete
- ✅ Monitoring in place
- ✅ Backup procedures verified

---

**🎉 When all tests pass, the system is ready for production use!**
