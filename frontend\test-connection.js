#!/usr/bin/env node

/**
 * Simple script to test backend API connection
 */

const https = require('https');

const API_URL = 'https://crm-api-gateway.onrender.com';

console.log('🔍 Testing backend API connection...\n');

// Test health endpoint
function testHealthEndpoint() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'crm-api-gateway.onrender.com',
      port: 443,
      path: '/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test login endpoint
function testLoginEndpoint() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    });

    const options = {
      hostname: 'crm-api-gateway.onrender.com',
      port: 443,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// Run tests
async function runTests() {
  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing health endpoint...');
    const healthResult = await testHealthEndpoint();
    
    if (healthResult.status === 200) {
      console.log('✅ Health check passed');
      console.log(`   Response: ${healthResult.data}\n`);
    } else {
      console.log(`❌ Health check failed (Status: ${healthResult.status})`);
      console.log(`   Response: ${healthResult.data}\n`);
    }

    // Test 2: Login Endpoint
    console.log('2️⃣ Testing login endpoint...');
    const loginResult = await testLoginEndpoint();
    
    if (loginResult.status === 200) {
      console.log('✅ Login endpoint working');
      const response = JSON.parse(loginResult.data);
      if (response.success && response.data && response.data.token) {
        console.log('✅ Authentication successful');
        console.log(`   User: ${response.data.user.full_name}`);
        console.log(`   Role: ${response.data.user.role}\n`);
      } else {
        console.log('❌ Authentication failed - Invalid response format\n');
      }
    } else {
      console.log(`❌ Login failed (Status: ${loginResult.status})`);
      console.log(`   Response: ${loginResult.data}\n`);
    }

    console.log('🎉 Backend connection test completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: cd frontend && npm install');
    console.log('   2. Run: npm run dev');
    console.log('   3. Open: http://localhost:3000');
    console.log('   4. Login with: <EMAIL> / admin123');

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   - Check your internet connection');
    console.log('   - Verify backend services are running');
    console.log('   - Check if API Gateway is accessible');
  }
}

runTests();
