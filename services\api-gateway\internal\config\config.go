package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	sharedConfig "github.com/crm-microservices/shared/config"
	"github.com/joho/godotenv"
)

// Config holds configuration for the API Gateway
type Config struct {
	*sharedConfig.Config

	// API Gateway specific configuration
	Service  ServiceConfig
	Services ServicesConfig
}

// ServiceConfig holds API Gateway service configuration
type ServiceConfig struct {
	Name    string
	Version string
	Port    string
}

// ServicesConfig holds configuration for downstream services
type ServicesConfig struct {
	AuthServiceURL         string
	AdminServiceURL        string
	StaffServiceURL        string
	PaymentServiceURL      string
	NotificationServiceURL string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists (ignore errors as it's optional)
	_ = godotenv.Load()

	// Load shared configuration
	sharedCfg, err := sharedConfig.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load shared config: %w", err)
	}

	// Override server port for API Gateway
	sharedCfg.Server.Port = getEnv("API_GATEWAY_PORT", "8080")

	config := &Config{
		Config: sharedCfg,
		Service: ServiceConfig{
			Name:    "api-gateway",
			Version: getEnv("SERVICE_VERSION", "1.0.0"),
			Port:    sharedCfg.Server.Port,
		},
		Services: ServicesConfig{
			AuthServiceURL:         normalizeServiceURL(getEnv("AUTH_SERVICE_URL", "http://localhost:8081")),
			AdminServiceURL:        normalizeServiceURL(getEnv("ADMIN_SERVICE_URL", "")),
			StaffServiceURL:        normalizeServiceURL(getEnv("STAFF_SERVICE_URL", "")),
			PaymentServiceURL:      normalizeServiceURL(getEnv("PAYMENT_SERVICE_URL", "")),
			NotificationServiceURL: normalizeServiceURL(getEnv("NOTIFICATION_SERVICE_URL", "")),
		},
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate shared configuration
	if err := c.Config.Validate(); err != nil {
		return err
	}

	// Validate API Gateway specific configuration
	if c.Service.Name == "" {
		return fmt.Errorf("service name must be set")
	}

	if c.Service.Port == "" {
		return fmt.Errorf("service port must be set")
	}

	// Validate port is a valid number
	if _, err := strconv.Atoi(c.Service.Port); err != nil {
		return fmt.Errorf("service port must be a valid number: %w", err)
	}

	// Validate that at least auth service URL is configured
	if c.Services.AuthServiceURL == "" {
		return fmt.Errorf("auth service URL must be configured")
	}

	return nil
}

// GetServiceAddr returns the service address
func (c *Config) GetServiceAddr() string {
	return fmt.Sprintf("%s:%s", c.Server.Host, c.Service.Port)
}

// Helper functions

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// normalizeServiceURL ensures service URLs have proper protocol for Render deployment
func normalizeServiceURL(url string) string {
	if url == "" {
		return ""
	}

	// If URL already has protocol, return as-is
	if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
		return url
	}

	// For Render deployment, add https:// protocol
	// Render services are accessible via HTTPS
	if strings.Contains(url, ".onrender.com") {
		return "https://" + url
	}

	// For localhost or other development URLs, add http://
	if strings.Contains(url, "localhost") || strings.Contains(url, "127.0.0.1") {
		return "http://" + url
	}

	// Default to https for production
	return "https://" + url
}
