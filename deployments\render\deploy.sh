#!/bin/bash

# 🚀 Render Deployment Script for Go Docker Platform
# Deploys all microservices to Render platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
RENDER_API_KEY="${RENDER_API_KEY}"
RENDER_API_URL="https://api.render.com/v1"
PROJECT_NAME="go-docker-platform"
ENVIRONMENT="production"

# Service configurations
declare -A SERVICES=(
    ["auth-service"]="crm-auth-service"
    ["admin-service"]="crm-admin-service"
    ["staff-service"]="crm-staff-service"
    ["payment-service"]="crm-payment-service"
    ["notification-service"]="crm-notification-service"
    ["api-gateway"]="crm-api-gateway"
)

# Utility functions
print_status() {
    local status=$1
    local message=$2
    case $status in
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "step")
            echo -e "\n${BLUE}🔄 $message${NC}"
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    print_status "step" "Checking deployment prerequisites"
    
    # Check if Render API key is set
    if [ -z "$RENDER_API_KEY" ]; then
        print_status "error" "RENDER_API_KEY environment variable is not set"
        echo "Please set your Render API key:"
        echo "export RENDER_API_KEY=your_api_key_here"
        exit 1
    fi
    
    # Check if required tools are installed
    if ! command -v curl &> /dev/null; then
        print_status "error" "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_status "warning" "jq is not installed. JSON parsing will be limited."
    fi
    
    print_status "success" "Prerequisites check passed"
}

# Create or update service
deploy_service() {
    local service_name=$1
    local render_service_name=$2
    
    print_status "step" "Deploying $service_name to Render"
    
    # Check if service exists
    local service_id
    service_id=$(curl -s \
        -H "Authorization: Bearer $RENDER_API_KEY" \
        "$RENDER_API_URL/services" | \
        jq -r ".[] | select(.name == \"$render_service_name\") | .id" 2>/dev/null || echo "")
    
    if [ -n "$service_id" ] && [ "$service_id" != "null" ]; then
        print_status "info" "Service $render_service_name exists. Triggering deployment..."
        
        # Trigger deployment
        local deploy_response
        deploy_response=$(curl -s -w "\n%{http_code}" \
            -X POST \
            -H "Authorization: Bearer $RENDER_API_KEY" \
            -H "Content-Type: application/json" \
            "$RENDER_API_URL/services/$service_id/deploys")
        
        local status_code=$(echo "$deploy_response" | tail -n1)
        local response_body=$(echo "$deploy_response" | head -n -1)
        
        if [ "$status_code" = "201" ]; then
            print_status "success" "Deployment triggered for $service_name"
            
            # Extract deploy ID if jq is available
            if command -v jq &> /dev/null; then
                local deploy_id=$(echo "$response_body" | jq -r '.id' 2>/dev/null || echo "")
                if [ -n "$deploy_id" ] && [ "$deploy_id" != "null" ]; then
                    print_status "info" "Deploy ID: $deploy_id"
                fi
            fi
        else
            print_status "error" "Failed to deploy $service_name. Status: $status_code"
            echo "Response: $response_body"
            return 1
        fi
    else
        print_status "warning" "Service $render_service_name not found. Please create it manually first."
        print_status "info" "Use the render.yaml blueprint to create the service."
        return 1
    fi
}

# Wait for deployment to complete
wait_for_deployment() {
    local service_name=$1
    local render_service_name=$2
    local max_wait=600  # 10 minutes
    local wait_time=0
    
    print_status "info" "Waiting for $service_name deployment to complete..."
    
    # Get service ID
    local service_id
    service_id=$(curl -s \
        -H "Authorization: Bearer $RENDER_API_KEY" \
        "$RENDER_API_URL/services" | \
        jq -r ".[] | select(.name == \"$render_service_name\") | .id" 2>/dev/null || echo "")
    
    if [ -z "$service_id" ] || [ "$service_id" = "null" ]; then
        print_status "warning" "Cannot monitor deployment - service not found"
        return 1
    fi
    
    while [ $wait_time -lt $max_wait ]; do
        # Get latest deployment status
        local deploy_status
        deploy_status=$(curl -s \
            -H "Authorization: Bearer $RENDER_API_KEY" \
            "$RENDER_API_URL/services/$service_id/deploys?limit=1" | \
            jq -r '.[0].status' 2>/dev/null || echo "unknown")
        
        case $deploy_status in
            "live")
                print_status "success" "$service_name deployment completed successfully"
                return 0
                ;;
            "build_failed"|"update_failed"|"canceled")
                print_status "error" "$service_name deployment failed with status: $deploy_status"
                return 1
                ;;
            "created"|"build_in_progress"|"update_in_progress")
                echo -n "."
                sleep 10
                wait_time=$((wait_time + 10))
                ;;
            *)
                print_status "warning" "Unknown deployment status: $deploy_status"
                sleep 10
                wait_time=$((wait_time + 10))
                ;;
        esac
    done
    
    print_status "warning" "Deployment monitoring timed out for $service_name"
    return 1
}

# Deploy all services
deploy_all_services() {
    print_status "step" "Starting deployment of all services"
    
    local failed_deployments=()
    local successful_deployments=()
    
    # Deploy services in dependency order
    local deployment_order=("auth-service" "admin-service" "staff-service" "payment-service" "notification-service" "api-gateway")
    
    for service in "${deployment_order[@]}"; do
        local render_service_name="${SERVICES[$service]}"
        
        if deploy_service "$service" "$render_service_name"; then
            successful_deployments+=("$service")
            
            # Wait for critical services to be ready before proceeding
            if [[ "$service" == "auth-service" ]]; then
                wait_for_deployment "$service" "$render_service_name"
            fi
        else
            failed_deployments+=("$service")
        fi
        
        # Small delay between deployments
        sleep 5
    done
    
    # Report results
    print_status "step" "Deployment Summary"
    
    if [ ${#successful_deployments[@]} -gt 0 ]; then
        print_status "success" "Successfully deployed services:"
        for service in "${successful_deployments[@]}"; do
            echo "  ✅ $service"
        done
    fi
    
    if [ ${#failed_deployments[@]} -gt 0 ]; then
        print_status "error" "Failed to deploy services:"
        for service in "${failed_deployments[@]}"; do
            echo "  ❌ $service"
        done
        return 1
    fi
    
    print_status "success" "All services deployed successfully!"
}

# Verify deployment
verify_deployment() {
    print_status "step" "Verifying deployment health"
    
    # List of service health endpoints
    declare -A HEALTH_ENDPOINTS=(
        ["auth-service"]="https://crm-auth-service.onrender.com/health"
        ["admin-service"]="https://crm-admin-service.onrender.com/health"
        ["staff-service"]="https://crm-staff-service.onrender.com/health"
        ["payment-service"]="https://crm-payment-service.onrender.com/health"
        ["notification-service"]="https://crm-notification-service.onrender.com/health"
        ["api-gateway"]="https://crm-api-gateway.onrender.com/health"
    )
    
    local healthy_services=()
    local unhealthy_services=()
    
    for service in "${!HEALTH_ENDPOINTS[@]}"; do
        local endpoint="${HEALTH_ENDPOINTS[$service]}"
        
        print_status "info" "Checking health of $service..."
        
        if curl -s -f "$endpoint" > /dev/null 2>&1; then
            print_status "success" "$service is healthy"
            healthy_services+=("$service")
        else
            print_status "warning" "$service health check failed"
            unhealthy_services+=("$service")
        fi
    done
    
    # Report health status
    echo ""
    print_status "info" "Health Check Summary:"
    echo "  Healthy: ${#healthy_services[@]}"
    echo "  Unhealthy: ${#unhealthy_services[@]}"
    
    if [ ${#unhealthy_services[@]} -gt 0 ]; then
        print_status "warning" "Some services may still be starting up. Check again in a few minutes."
    fi
}

# Main execution
main() {
    print_status "step" "Starting Render deployment for Go Docker Platform"
    
    # Check prerequisites
    check_prerequisites
    
    # Deploy all services
    if deploy_all_services; then
        print_status "success" "Deployment completed successfully!"
        
        # Wait a bit for services to start
        print_status "info" "Waiting for services to start..."
        sleep 30
        
        # Verify deployment
        verify_deployment
        
        # Display useful information
        echo ""
        print_status "info" "Deployment Information:"
        echo "  🌐 API Gateway: https://crm-api-gateway.onrender.com"
        echo "  🔐 Auth Service: https://crm-auth-service.onrender.com"
        echo "  👥 Admin Service: https://crm-admin-service.onrender.com"
        echo "  📚 Staff Service: https://crm-staff-service.onrender.com"
        echo "  💳 Payment Service: https://crm-payment-service.onrender.com"
        echo "  📧 Notification Service: https://crm-notification-service.onrender.com"
        echo ""
        echo "  📊 Monitor your services at: https://dashboard.render.com"
        echo ""
        
    else
        print_status "error" "Deployment failed. Check the logs above for details."
        exit 1
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "verify")
        verify_deployment
        ;;
    "help")
        echo "Usage: $0 [deploy|verify|help]"
        echo "  deploy  - Deploy all services (default)"
        echo "  verify  - Verify deployment health"
        echo "  help    - Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
