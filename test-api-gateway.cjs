const https = require('https');

console.log('🔍 Testing API Gateway...');

// Test API Gateway health endpoint
const options = {
  hostname: 'crm-api-gateway.onrender.com',
  port: 443,
  path: '/health',
  method: 'GET',
  timeout: 10000
};

const req = https.request(options, (res) => {
  console.log(`✅ API Gateway Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
  });
});

req.on('error', (error) => {
  console.log(`❌ API Gateway Error: ${error.message}`);
});

req.on('timeout', () => {
  console.log('❌ API Gateway Timeout');
  req.destroy();
});

req.end();
