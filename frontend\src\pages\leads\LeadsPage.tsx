import { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  UserPlusIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  UserGroupIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import {
  Lead,
  LeadStatus,
  LeadSource,
  LeadPriority,
  LeadCreateRequest,
  LeadUpdateRequest,
  LeadFilters,
  LeadStats
} from '@/types/student'

// Mock data for development
const mockLeads: Lead[] = [
  {
    id: '1',
    lead_number: 'LEAD001',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    full_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+998901234567',
    date_of_birth: '2005-08-15',
    gender: 'FEMAL<PERSON>',
    address: '123 Oak Street',
    city: 'Tashkent',
    country: 'Uzbekistan',
    status: 'NEW',
    source: 'WEBSITE',
    priority: 'HIGH',
    interested_courses: ['Advanced Mathematics', 'Computer Science'],
    budget_range: '$1000-$1500',
    preferred_start_date: '2024-03-01',
    parent_name: 'Robert Johnson',
    parent_phone: '+998901234568',
    parent_email: '<EMAIL>',
    assigned_to: 'staff1',
    assigned_to_name: 'Staff Member',
    conversion_probability: 75,
    initial_notes: 'Very interested in STEM courses. Parent is supportive.',
    tags: ['STEM', 'High-potential'],
    created_at: '2024-01-20T10:30:00Z',
    updated_at: '2024-01-20T10:30:00Z',
    created_by: 'reception'
  },
  {
    id: '2',
    lead_number: 'LEAD002',
    first_name: 'Michael',
    last_name: 'Brown',
    full_name: 'Michael Brown',
    email: '<EMAIL>',
    phone: '+998901234569',
    address: '456 Pine Avenue',
    city: 'Samarkand',
    country: 'Uzbekistan',
    status: 'CONTACTED',
    source: 'REFERRAL',
    priority: 'MEDIUM',
    interested_courses: ['English Literature'],
    budget_range: '$800-$1200',
    preferred_start_date: '2024-02-15',
    assigned_to: 'staff2',
    assigned_to_name: 'Another Staff',
    last_contact_date: '2024-01-22T14:15:00Z',
    next_follow_up_date: '2024-01-29T10:00:00Z',
    conversion_probability: 60,
    initial_notes: 'Referred by current student. Interested in literature program.',
    tags: ['Referral', 'Literature'],
    created_at: '2024-01-21T09:15:00Z',
    updated_at: '2024-01-22T14:15:00Z',
    created_by: 'reception'
  },
  {
    id: '3',
    lead_number: 'LEAD003',
    first_name: 'Emma',
    last_name: 'Davis',
    full_name: 'Emma Davis',
    phone: '+998901234570',
    date_of_birth: '2006-03-10',
    gender: 'FEMALE',
    city: 'Bukhara',
    country: 'Uzbekistan',
    status: 'QUALIFIED',
    source: 'SOCIAL_MEDIA',
    priority: 'HIGH',
    interested_courses: ['Art & Design', 'Photography'],
    budget_range: '$600-$1000',
    preferred_start_date: '2024-04-01',
    parent_name: 'Jennifer Davis',
    parent_phone: '+998901234571',
    assigned_to: 'staff1',
    assigned_to_name: 'Staff Member',
    last_contact_date: '2024-01-25T11:30:00Z',
    next_follow_up_date: '2024-02-01T15:00:00Z',
    conversion_probability: 85,
    initial_notes: 'Very talented in arts. Portfolio review scheduled.',
    tags: ['Arts', 'Portfolio-review'],
    created_at: '2024-01-23T16:45:00Z',
    updated_at: '2024-01-25T11:30:00Z',
    created_by: 'marketing'
  },
  {
    id: '4',
    lead_number: 'LEAD004',
    first_name: 'David',
    last_name: 'Wilson',
    full_name: 'David Wilson',
    email: '<EMAIL>',
    phone: '+998901234572',
    status: 'LOST',
    source: 'ADVERTISEMENT',
    priority: 'LOW',
    interested_courses: ['Business Studies'],
    budget_range: '$500-$800',
    assigned_to: 'staff2',
    assigned_to_name: 'Another Staff',
    last_contact_date: '2024-01-18T13:20:00Z',
    conversion_probability: 10,
    initial_notes: 'Budget constraints. Not ready to commit at this time.',
    tags: ['Budget-constraint', 'Future-prospect'],
    created_at: '2024-01-15T12:00:00Z',
    updated_at: '2024-01-18T13:20:00Z',
    created_by: 'marketing'
  }
]

const mockStats: LeadStats = {
  total_leads: 156,
  new_leads_this_month: 23,
  qualified_leads: 45,
  converted_leads: 18,
  conversion_rate: 11.5,
  average_conversion_time: 14,
  leads_by_source: {
    WEBSITE: 45,
    REFERRAL: 32,
    SOCIAL_MEDIA: 28,
    ADVERTISEMENT: 21,
    WALK_IN: 15,
    PHONE_CALL: 10,
    EMAIL: 3,
    OTHER: 2
  },
  leads_by_status: {
    NEW: 34,
    CONTACTED: 28,
    QUALIFIED: 25,
    PROPOSAL: 15,
    NEGOTIATION: 12,
    ENROLLED: 18,
    LOST: 20,
    FOLLOW_UP: 4
  }
}

const LeadsPage = () => {
  const [leads, setLeads] = useState<Lead[]>([])
  const [stats, setStats] = useState<LeadStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<LeadFilters>({
    search: '',
    status: 'ALL',
    source: 'ALL',
    priority: 'ALL',
    assigned_to: 'ALL',
    date_from: '',
    date_to: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null)

  // Load leads on component mount
  useEffect(() => {
    loadLeads()
    loadStats()
  }, [])

  const loadLeads = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setLeads(mockLeads)
    } catch (error) {
      console.error('Failed to load leads:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Filter leads based on search and filters
  const filteredLeads = leads.filter(lead => {
    const matchesSearch =
      lead.full_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      lead.lead_number.toLowerCase().includes(filters.search.toLowerCase()) ||
      lead.email?.toLowerCase().includes(filters.search.toLowerCase()) ||
      lead.phone.includes(filters.search) ||
      lead.interested_courses.some(course =>
        course.toLowerCase().includes(filters.search.toLowerCase())
      )

    const matchesStatus = filters.status === 'ALL' || lead.status === filters.status
    const matchesSource = filters.source === 'ALL' || lead.source === filters.source
    const matchesPriority = filters.priority === 'ALL' || lead.priority === filters.priority
    const matchesAssignedTo = filters.assigned_to === 'ALL' || lead.assigned_to === filters.assigned_to

    let matchesDateRange = true
    if (filters.date_from && filters.date_to) {
      const leadDate = new Date(lead.created_at)
      const fromDate = new Date(filters.date_from)
      const toDate = new Date(filters.date_to)
      matchesDateRange = leadDate >= fromDate && leadDate <= toDate
    }

    return matchesSearch && matchesStatus && matchesSource && matchesPriority && matchesAssignedTo && matchesDateRange
  })

  const handleCreateLead = async (leadData: LeadCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newLead: Lead = {
        id: Date.now().toString(),
        lead_number: `LEAD${String(leads.length + 1).padStart(3, '0')}`,
        ...leadData,
        full_name: `${leadData.first_name} ${leadData.last_name}`,
        status: 'NEW',
        conversion_probability: 50,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 'current_user'
      }

      setLeads(prev => [...prev, newLead])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create lead:', error)
    }
  }

  const handleUpdateLead = async (leadId: string, leadData: LeadUpdateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setLeads(prev => prev.map(lead =>
        lead.id === leadId
          ? {
              ...lead,
              ...leadData,
              full_name: leadData.first_name && leadData.last_name
                ? `${leadData.first_name} ${leadData.last_name}`
                : lead.full_name,
              updated_at: new Date().toISOString()
            }
          : lead
      ))
      setShowEditModal(false)
      setSelectedLead(null)
    } catch (error) {
      console.error('Failed to update lead:', error)
    }
  }

  const handleDeleteLead = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead? This action cannot be undone.')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setLeads(prev => prev.filter(lead => lead.id !== leadId))
    } catch (error) {
      console.error('Failed to delete lead:', error)
    }
  }

  const handleConvertToStudent = async (leadId: string) => {
    if (!confirm('Convert this lead to a student? This will create a new student record.')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setLeads(prev => prev.map(lead =>
        lead.id === leadId
          ? {
              ...lead,
              status: 'ENROLLED',
              converted_to_student_id: `STU${Date.now()}`,
              conversion_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          : lead
      ))
    } catch (error) {
      console.error('Failed to convert lead:', error)
    }
  }

  const getStatusBadgeColor = (status: LeadStatus) => {
    const colors = {
      NEW: 'bg-blue-100 text-blue-800',
      CONTACTED: 'bg-yellow-100 text-yellow-800',
      QUALIFIED: 'bg-green-100 text-green-800',
      PROPOSAL: 'bg-purple-100 text-purple-800',
      NEGOTIATION: 'bg-orange-100 text-orange-800',
      ENROLLED: 'bg-emerald-100 text-emerald-800',
      LOST: 'bg-red-100 text-red-800',
      FOLLOW_UP: 'bg-indigo-100 text-indigo-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  const getPriorityBadgeColor = (priority: LeadPriority) => {
    const colors = {
      LOW: 'bg-gray-100 text-gray-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      URGENT: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
  }

  const getSourceBadgeColor = (source: LeadSource) => {
    const colors = {
      WEBSITE: 'bg-blue-100 text-blue-800',
      REFERRAL: 'bg-green-100 text-green-800',
      SOCIAL_MEDIA: 'bg-purple-100 text-purple-800',
      ADVERTISEMENT: 'bg-yellow-100 text-yellow-800',
      WALK_IN: 'bg-indigo-100 text-indigo-800',
      PHONE_CALL: 'bg-orange-100 text-orange-800',
      EMAIL: 'bg-cyan-100 text-cyan-800',
      OTHER: 'bg-gray-100 text-gray-800'
    }
    return colors[source] || 'bg-gray-100 text-gray-800'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const isOverdue = (followUpDate?: string) => {
    if (!followUpDate) return false
    return new Date(followUpDate) < new Date()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Lead Management</h1>
          <p className="text-gray-600 mt-1">
            Track and manage potential students through the enrollment pipeline
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Lead
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <UserGroupIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Leads</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_leads}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <UserPlusIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">New This Month</p>
                <p className="text-xl font-semibold text-gray-900">{stats.new_leads_this_month}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Qualified Leads</p>
                <p className="text-xl font-semibold text-gray-900">{stats.qualified_leads}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-emerald-100 rounded-lg">
                <TrendingUpIcon className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Converted</p>
                <p className="text-xl font-semibold text-gray-900">{stats.converted_leads}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Conversion Rate</p>
                <p className="text-xl font-semibold text-gray-900">{stats.conversion_rate}%</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <ClockIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg. Conversion Time</p>
                <p className="text-xl font-semibold text-gray-900">{stats.average_conversion_time} days</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Follow-ups Due</p>
                <p className="text-xl font-semibold text-gray-900">
                  {leads.filter(lead => lead.next_follow_up_date && isOverdue(lead.next_follow_up_date)).length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <UserGroupIcon className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">High Priority</p>
                <p className="text-xl font-semibold text-gray-900">
                  {leads.filter(lead => lead.priority === 'HIGH' || lead.priority === 'URGENT').length}
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search leads..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as LeadStatus | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="NEW">New</option>
            <option value="CONTACTED">Contacted</option>
            <option value="QUALIFIED">Qualified</option>
            <option value="PROPOSAL">Proposal</option>
            <option value="NEGOTIATION">Negotiation</option>
            <option value="ENROLLED">Enrolled</option>
            <option value="LOST">Lost</option>
            <option value="FOLLOW_UP">Follow Up</option>
          </select>

          <select
            value={filters.source}
            onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value as LeadSource | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Sources</option>
            <option value="WEBSITE">Website</option>
            <option value="REFERRAL">Referral</option>
            <option value="SOCIAL_MEDIA">Social Media</option>
            <option value="ADVERTISEMENT">Advertisement</option>
            <option value="WALK_IN">Walk In</option>
            <option value="PHONE_CALL">Phone Call</option>
            <option value="EMAIL">Email</option>
            <option value="OTHER">Other</option>
          </select>

          <select
            value={filters.priority}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value as LeadPriority | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Priorities</option>
            <option value="LOW">Low</option>
            <option value="MEDIUM">Medium</option>
            <option value="HIGH">High</option>
            <option value="URGENT">Urgent</option>
          </select>

          <select
            value={filters.assigned_to}
            onChange={(e) => setFilters(prev => ({ ...prev, assigned_to: e.target.value }))}
            className="input"
          >
            <option value="ALL">All Staff</option>
            <option value="staff1">Staff Member</option>
            <option value="staff2">Another Staff</option>
            <option value="unassigned">Unassigned</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredLeads.length} of {leads.length} leads
          </div>
        </div>
      </div>

      {/* Leads Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-head">Lead</th>
                <th className="table-head">Contact</th>
                <th className="table-head">Status</th>
                <th className="table-head">Priority</th>
                <th className="table-head">Source</th>
                <th className="table-head">Interested Courses</th>
                <th className="table-head">Assigned To</th>
                <th className="table-head">Next Follow-up</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredLeads.length === 0 ? (
                <tr>
                  <td colSpan={9} className="table-cell text-center py-8 text-gray-500">
                    No leads found matching your criteria
                  </td>
                </tr>
              ) : (
                filteredLeads.map((lead) => (
                  <tr key={lead.id} className="table-row">
                    <td className="table-cell">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-primary-600 font-medium text-sm">
                            {lead.first_name[0]}{lead.last_name[0]}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{lead.full_name}</div>
                          <div className="text-sm text-gray-500">{lead.lead_number}</div>
                          {lead.conversion_probability && (
                            <div className="text-xs text-gray-400">
                              {lead.conversion_probability}% conversion probability
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm">
                        <div className="flex items-center gap-1 text-gray-900">
                          <PhoneIcon className="h-3 w-3" />
                          {lead.phone}
                        </div>
                        {lead.email && (
                          <div className="flex items-center gap-1 text-gray-500">
                            <EnvelopeIcon className="h-3 w-3" />
                            {lead.email}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <Badge className={getStatusBadgeColor(lead.status)}>
                        {lead.status.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <Badge className={getPriorityBadgeColor(lead.priority)}>
                        {lead.priority}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <Badge className={getSourceBadgeColor(lead.source)}>
                        {lead.source.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900 max-w-xs">
                        {lead.interested_courses.slice(0, 2).join(', ')}
                        {lead.interested_courses.length > 2 && (
                          <span className="text-gray-500"> +{lead.interested_courses.length - 2} more</span>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {lead.assigned_to_name || 'Unassigned'}
                      </div>
                    </td>
                    <td className="table-cell">
                      {lead.next_follow_up_date ? (
                        <div className={`text-sm ${isOverdue(lead.next_follow_up_date) ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                          {formatDate(lead.next_follow_up_date)}
                          {isOverdue(lead.next_follow_up_date) && (
                            <div className="text-xs text-red-500">Overdue</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Not scheduled</span>
                      )}
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedLead(lead)
                            setShowViewModal(true)
                          }}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedLead(lead)
                            setShowEditModal(true)
                          }}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        {lead.status !== 'ENROLLED' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleConvertToStudent(lead.id)}
                            className="text-green-600 hover:text-green-700"
                            title="Convert to Student"
                          >
                            <UserPlusIcon className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteLead(lead.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
      {/* Modals */}
      {showCreateModal && (
        <LeadCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateLead}
        />
      )}

      {showEditModal && selectedLead && (
        <LeadEditModal
          lead={selectedLead}
          onClose={() => {
            setShowEditModal(false)
            setSelectedLead(null)
          }}
          onSubmit={(leadData) => handleUpdateLead(selectedLead.id, leadData)}
        />
      )}

      {showViewModal && selectedLead && (
        <LeadViewModal
          lead={selectedLead}
          onClose={() => {
            setShowViewModal(false)
            setSelectedLead(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (placeholder implementations)
const LeadCreateModal = ({ onClose, onSubmit }: {
  onClose: () => void
  onSubmit: (data: LeadCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Add New Lead</h2>
        <p className="text-gray-600 mb-4">Lead creation form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Add Lead</Button>
        </div>
      </div>
    </div>
  )
}

const LeadEditModal = ({ lead, onClose, onSubmit }: {
  lead: Lead
  onClose: () => void
  onSubmit: (data: LeadUpdateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Edit Lead: {lead.full_name}</h2>
        <p className="text-gray-600 mb-4">Lead editing form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}

const LeadViewModal = ({ lead, onClose }: {
  lead: Lead
  onClose: () => void
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Lead Details: {lead.full_name}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Personal Information</h3>
            <div className="space-y-1">
              <p><strong>Lead Number:</strong> {lead.lead_number}</p>
              <p><strong>Name:</strong> {lead.full_name}</p>
              <p><strong>Email:</strong> {lead.email || 'Not provided'}</p>
              <p><strong>Phone:</strong> {lead.phone}</p>
              {lead.date_of_birth && (
                <p><strong>Date of Birth:</strong> {formatDate(lead.date_of_birth)}</p>
              )}
              {lead.gender && <p><strong>Gender:</strong> {lead.gender}</p>}
              {lead.address && <p><strong>Address:</strong> {lead.address}, {lead.city}</p>}
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Lead Information</h3>
            <div className="space-y-1">
              <p><strong>Status:</strong> {lead.status.replace('_', ' ')}</p>
              <p><strong>Source:</strong> {lead.source.replace('_', ' ')}</p>
              <p><strong>Priority:</strong> {lead.priority}</p>
              <p><strong>Assigned To:</strong> {lead.assigned_to_name || 'Unassigned'}</p>
              {lead.conversion_probability && (
                <p><strong>Conversion Probability:</strong> {lead.conversion_probability}%</p>
              )}
              <p><strong>Created:</strong> {formatDate(lead.created_at)}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Course Interest</h3>
            <div className="space-y-1">
              <p><strong>Interested Courses:</strong></p>
              <ul className="list-disc list-inside text-gray-600">
                {lead.interested_courses.map((course, index) => (
                  <li key={index}>{course}</li>
                ))}
              </ul>
              {lead.budget_range && (
                <p><strong>Budget Range:</strong> {lead.budget_range}</p>
              )}
              {lead.preferred_start_date && (
                <p><strong>Preferred Start:</strong> {formatDate(lead.preferred_start_date)}</p>
              )}
            </div>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Follow-up & Dates</h3>
            <div className="space-y-1">
              {lead.last_contact_date && (
                <p><strong>Last Contact:</strong> {formatDate(lead.last_contact_date)}</p>
              )}
              {lead.next_follow_up_date && (
                <p><strong>Next Follow-up:</strong> {formatDate(lead.next_follow_up_date)}</p>
              )}
              {lead.conversion_date && (
                <p><strong>Conversion Date:</strong> {formatDate(lead.conversion_date)}</p>
              )}
            </div>
          </div>
          {(lead.parent_name || lead.parent_phone) && (
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Parent/Guardian</h3>
              <div className="space-y-1">
                {lead.parent_name && <p><strong>Name:</strong> {lead.parent_name}</p>}
                {lead.parent_phone && <p><strong>Phone:</strong> {lead.parent_phone}</p>}
                {lead.parent_email && <p><strong>Email:</strong> {lead.parent_email}</p>}
              </div>
            </div>
          )}
          {lead.tags && lead.tags.length > 0 && (
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Tags</h3>
              <div className="flex flex-wrap gap-1">
                {lead.tags.map((tag, index) => (
                  <Badge key={index} className="bg-gray-100 text-gray-800 text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          {lead.initial_notes && (
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-600">{lead.initial_notes}</p>
            </div>
          )}
        </div>
        <div className="mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default LeadsPage
