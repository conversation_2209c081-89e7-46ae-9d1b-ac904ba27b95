const https = require('https');

console.log('🔍 Testing Missing Services...\n');

const services = [
  { name: 'Payment Service', url: 'crm-payment-service.onrender.com' },
  { name: 'Notification Service', url: 'crm-notification-service.onrender.com' }
];

async function testService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: service.url,
      port: 443,
      path: '/health',
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          name: service.name,
          status: res.statusCode,
          response: data,
          success: res.statusCode === 200
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: service.name,
        status: 'ERROR',
        response: error.message,
        success: false
      });
    });

    req.on('timeout', () => {
      resolve({
        name: service.name,
        status: 'TIMEOUT',
        response: 'Request timeout',
        success: false
      });
      req.destroy();
    });

    req.end();
  });
}

async function testMissingServices() {
  console.log('Testing potentially missing services...\n');
  
  for (const service of services) {
    console.log(`Testing ${service.name}...`);
    const result = await testService(service);
    
    if (result.success) {
      console.log(`✅ ${result.name}: Status ${result.status}`);
    } else {
      console.log(`❌ ${result.name}: ${result.status} - ${result.response.substring(0, 100)}`);
    }
    console.log('');
  }
}

testMissingServices().catch(console.error);
