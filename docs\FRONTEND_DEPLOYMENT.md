# 🚀 Frontend Deployment Guide

Complete guide for deploying the React frontend of the Go Docker Platform CRM system.

## 📋 Overview

The React frontend is designed to be deployed alongside the existing Go microservices on the Render platform. It provides a modern, responsive interface for the CRM system with role-based access control.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │   API Gateway   │    │  Microservices  │
│   (Frontend)    │────│   (Routing)     │────│   (Backend)     │
│   Port: 80      │    │   Port: 8080    │    │  Ports: 8081-85 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

### Local Development
- Node.js 18+
- npm 9+
- Git

### Production Deployment
- Render account
- GitHub repository access
- Domain name (optional)

## 🚀 Deployment Steps

### 1. Prepare the Frontend

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Build for production
npm run build

# Test the build locally
npm run preview
```

### 2. Update Render Configuration

The `render.yaml` file has been updated to include the React frontend:

```yaml
# Frontend Service
- type: web
  name: crm-frontend
  runtime: docker
  dockerfilePath: ./frontend/Dockerfile
  dockerContext: ./frontend
  envVars:
    - key: VITE_API_URL
      fromService:
        type: web
        name: crm-api-gateway
        property: host
    - key: VITE_API_VERSION
      value: v1
    - key: VITE_APP_NAME
      value: CRM Dashboard
    - key: VITE_APP_VERSION
      value: "1.0.0"
    - key: VITE_NODE_ENV
      value: production
  healthCheckPath: /health
```

### 3. Deploy to Render

#### Option A: Automatic Deployment (Recommended)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Add React frontend"
   git push origin main
   ```

2. **Render will automatically**:
   - Detect the updated `render.yaml`
   - Build the Docker container
   - Deploy the frontend service
   - Configure environment variables

#### Option B: Manual Deployment

1. **Create New Web Service** in Render Dashboard:
   - **Name**: `crm-frontend`
   - **Runtime**: Docker
   - **Dockerfile Path**: `./frontend/Dockerfile`
   - **Docker Context**: `./frontend`

2. **Configure Environment Variables**:
   ```
   VITE_API_URL=https://crm-api-gateway.onrender.com
   VITE_API_VERSION=v1
   VITE_APP_NAME=CRM Dashboard
   VITE_APP_VERSION=1.0.0
   VITE_NODE_ENV=production
   ```

3. **Deploy**: Click "Create Web Service"

### 4. Update CORS Configuration

The API Gateway CORS configuration has been updated to include the frontend URL:

```yaml
- key: CORS_ORIGINS
  fromService:
    type: web
    name: crm-frontend
    property: host
```

### 5. Verify Deployment

1. **Check Service Status**:
   - Visit Render Dashboard
   - Verify `crm-frontend` service is running
   - Check logs for any errors

2. **Test Frontend**:
   ```bash
   # Health check
   curl https://crm-frontend.onrender.com/health
   
   # Expected response: "healthy"
   ```

3. **Test Authentication**:
   - Visit the frontend URL
   - Login with demo credentials:
     - Email: `<EMAIL>`
     - Password: `admin123`

## 🔧 Configuration

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_API_URL` | Backend API Gateway URL | `https://crm-api-gateway.onrender.com` |
| `VITE_API_VERSION` | API version | `v1` |
| `VITE_APP_NAME` | Application name | `CRM Dashboard` |
| `VITE_APP_VERSION` | Application version | `1.0.0` |
| `VITE_NODE_ENV` | Environment | `production` |

### Docker Configuration

The frontend uses a multi-stage Docker build:

1. **Build Stage**: Compiles React app with Vite
2. **Production Stage**: Serves static files with Nginx

### Nginx Configuration

- **Gzip Compression**: Enabled for better performance
- **Security Headers**: Added for security
- **Client-side Routing**: Configured for React Router
- **Static Asset Caching**: 1-year cache for assets
- **Health Check**: `/health` endpoint

## 📊 Performance Optimizations

### Build Optimizations
- **Code Splitting**: Automatic route-based splitting
- **Tree Shaking**: Remove unused code
- **Asset Optimization**: Compressed images and fonts
- **Bundle Analysis**: Optimized chunk sizes

### Runtime Optimizations
- **React Query**: Efficient data fetching and caching
- **Lazy Loading**: Components loaded on demand
- **Memoization**: Optimized re-renders
- **Service Worker**: Caching strategies (future enhancement)

## 🔐 Security

### Authentication
- **JWT Tokens**: Secure token-based authentication
- **Token Refresh**: Automatic token renewal
- **Route Protection**: Role-based access control
- **Secure Storage**: Tokens stored in localStorage with validation

### Security Headers
- **X-Frame-Options**: Prevent clickjacking
- **X-XSS-Protection**: XSS protection
- **X-Content-Type-Options**: MIME type sniffing protection
- **Content-Security-Policy**: Content security policy

## 🐛 Troubleshooting

### Common Issues

#### 1. CORS Errors
**Problem**: Frontend requests blocked by CORS policy
**Solution**: 
- Verify CORS_ORIGINS includes frontend URL
- Check API Gateway configuration
- Restart API Gateway service

#### 2. Authentication Failures
**Problem**: Login not working
**Solution**:
- Check API Gateway URL in environment variables
- Verify backend services are running
- Check network connectivity

#### 3. Build Failures
**Problem**: Docker build fails
**Solution**:
- Check Node.js version compatibility
- Verify all dependencies are installed
- Review build logs for specific errors

#### 4. Routing Issues
**Problem**: 404 errors on page refresh
**Solution**:
- Verify Nginx configuration includes `try_files`
- Check React Router configuration
- Ensure all routes are properly defined

### Debug Commands

```bash
# Check service logs
render logs --service crm-frontend

# Test API connectivity
curl https://crm-api-gateway.onrender.com/health

# Check frontend health
curl https://crm-frontend.onrender.com/health

# Test authentication endpoint
curl -X POST https://crm-api-gateway.onrender.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 📈 Monitoring

### Health Checks
- **Frontend**: `/health` endpoint returns "healthy"
- **API Gateway**: Automatic health monitoring
- **Services**: Individual service health checks

### Performance Monitoring
- **Response Times**: Monitor API response times
- **Error Rates**: Track authentication and API errors
- **User Experience**: Monitor page load times

### Logging
- **Nginx Access Logs**: Request logging
- **Application Logs**: React error boundaries
- **API Logs**: Backend service logs

## 🔄 Updates and Maintenance

### Updating the Frontend

1. **Make Changes**: Update React components/features
2. **Test Locally**: `npm run dev` and `npm run build`
3. **Commit Changes**: Git commit and push
4. **Auto Deploy**: Render automatically deploys
5. **Verify**: Test the deployed application

### Rollback Procedure

1. **Identify Issue**: Monitor logs and user reports
2. **Rollback**: Use Render dashboard to rollback to previous deployment
3. **Fix Issue**: Address the problem in code
4. **Redeploy**: Push fixed version

## 🎯 Next Steps

### Immediate
- [ ] Test all user roles and permissions
- [ ] Verify all API integrations
- [ ] Monitor performance and errors

### Short Term
- [ ] Implement comprehensive error tracking
- [ ] Add performance monitoring
- [ ] Set up automated testing

### Long Term
- [ ] Progressive Web App (PWA) features
- [ ] React Native mobile app
- [ ] Advanced analytics and reporting

## 📞 Support

For deployment issues:
1. Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
2. Review service logs in Render dashboard
3. Verify API connectivity and CORS configuration
4. Contact the development team with specific error messages

---

**Frontend successfully deployed! 🎉**

The React frontend is now live and integrated with your Go Docker Platform CRM system.
