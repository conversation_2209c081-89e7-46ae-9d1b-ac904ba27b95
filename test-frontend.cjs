const https = require('https');

console.log('🔍 Testing Frontend Deployment...\n');

function testFrontend() {
  return new Promise((resolve) => {
    const options = {
      hostname: 'crm-frontend.onrender.com',
      port: 443,
      path: '/',
      method: 'GET',
      timeout: 15000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 500) // First 500 chars
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        status: 'ERROR',
        data: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        status: 'TIMEOUT',
        data: 'Request timeout'
      });
    });

    req.end();
  });
}

async function testFrontendDeployment() {
  console.log('Testing frontend at https://crm-frontend.onrender.com...');
  const result = await testFrontend();
  
  if (result.status === 200) {
    console.log(`✅ Frontend: Status ${result.status}`);
    console.log('   Content preview:', result.data);
    
    // Check if it's a React app
    if (result.data.includes('react') || result.data.includes('React') || result.data.includes('root')) {
      console.log('   ✅ Appears to be a React application');
    }
    
    // Check for API configuration
    if (result.data.includes('VITE_API_URL')) {
      console.log('   ✅ Contains API configuration');
    }
    
  } else {
    console.log(`❌ Frontend: Status ${result.status}`);
    console.log(`   Error: ${result.data}`);
  }
}

testFrontendDeployment().catch(console.error);
