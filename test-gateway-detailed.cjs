const https = require('https');

console.log('🔍 Detailed API Gateway Testing...\n');

// Test multiple endpoints
const endpoints = [
  { name: 'Health Check', path: '/health' },
  { name: 'Readiness Check', path: '/ready' },
  { name: 'Liveness Check', path: '/live' },
  { name: 'Version Check', path: '/version' },
  { name: 'Services Check', path: '/services' },
  { name: 'Metrics Check', path: '/metrics' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'crm-api-gateway.onrender.com',
      port: 443,
      path: endpoint.path,
      method: 'GET',
      timeout: 15000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          name: endpoint.name,
          path: endpoint.path,
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: endpoint.name,
        path: endpoint.path,
        status: 'ERROR',
        data: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: endpoint.name,
        path: endpoint.path,
        status: 'TIMEOUT',
        data: 'Request timeout'
      });
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log('Testing API Gateway endpoints...\n');
  
  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint.name} (${endpoint.path})...`);
    const result = await testEndpoint(endpoint);
    
    if (result.status === 200) {
      console.log(`✅ ${result.name}: Status ${result.status}`);
      try {
        const parsed = JSON.parse(result.data);
        if (parsed.data && parsed.data.services) {
          console.log('   Services status:');
          for (const [serviceName, serviceInfo] of Object.entries(parsed.data.services)) {
            console.log(`     ${serviceName}: ${serviceInfo.status || 'unknown'}`);
          }
        }
        if (parsed.data && parsed.data.checks && parsed.data.checks.services) {
          console.log('   Service checks:');
          for (const [serviceName, serviceInfo] of Object.entries(parsed.data.checks.services)) {
            console.log(`     ${serviceName}: ${serviceInfo.status || 'unknown'} (${serviceInfo.url || 'no url'})`);
          }
        }
      } catch (e) {
        console.log(`   Response: ${result.data.substring(0, 200)}...`);
      }
    } else {
      console.log(`❌ ${result.name}: Status ${result.status}`);
      console.log(`   Error: ${result.data.substring(0, 200)}...`);
    }
    console.log('');
  }
}

testAllEndpoints().catch(console.error);
