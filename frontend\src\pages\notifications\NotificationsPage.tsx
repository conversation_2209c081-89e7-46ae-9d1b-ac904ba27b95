import { useState, useEffect } from 'react'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  BellIcon,
  EnvelopeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Card } from '@/components/ui/Card'
import {
  Notification,
  NotificationType,
  NotificationChannel,
  NotificationStatus,
  NotificationPriority,
  NotificationCreateRequest,
  NotificationFilters,
  NotificationStats
} from '@/types/student'

// Mock data for development
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Payment Reminder',
    message: 'Your course payment is due in 3 days. Please make the payment to avoid late fees.',
    type: 'WARNING',
    priority: 'HIGH',
    status: 'UNREAD',
    channels: ['IN_APP', 'EMAIL', 'SMS'],
    recipient_id: 'student1',
    recipient_type: 'STUDENT',
    recipient_name: 'John Doe',
    category: 'PAYMENT',
    action_url: '/payments',
    action_text: 'Make Payment',
    expires_at: '2024-02-15T00:00:00Z',
    created_at: '2024-01-28T10:30:00Z',
    updated_at: '2024-01-28T10:30:00Z',
    created_by: 'system',
    created_by_name: 'System'
  },
  {
    id: '2',
    title: 'Course Enrollment Confirmed',
    message: 'Your enrollment in Advanced Mathematics has been confirmed. Classes start on February 1st.',
    type: 'SUCCESS',
    priority: 'MEDIUM',
    status: 'READ',
    channels: ['IN_APP', 'EMAIL'],
    recipient_id: 'student2',
    recipient_type: 'STUDENT',
    recipient_name: 'Sarah Smith',
    category: 'ENROLLMENT',
    action_url: '/courses',
    action_text: 'View Course',
    sent_at: '2024-01-25T14:15:00Z',
    delivered_at: '2024-01-25T14:16:00Z',
    read_at: '2024-01-25T15:30:00Z',
    created_at: '2024-01-25T14:15:00Z',
    updated_at: '2024-01-25T15:30:00Z',
    created_by: 'admin',
    created_by_name: 'Admin User'
  },
  {
    id: '3',
    title: 'System Maintenance Notice',
    message: 'The system will be under maintenance on February 2nd from 2:00 AM to 4:00 AM. Services may be temporarily unavailable.',
    type: 'INFO',
    priority: 'MEDIUM',
    status: 'READ',
    channels: ['IN_APP', 'EMAIL'],
    recipient_type: 'ALL_USERS',
    category: 'SYSTEM',
    expires_at: '2024-02-02T04:00:00Z',
    sent_at: '2024-01-26T09:00:00Z',
    delivered_at: '2024-01-26T09:01:00Z',
    read_at: '2024-01-26T10:15:00Z',
    created_at: '2024-01-26T09:00:00Z',
    updated_at: '2024-01-26T10:15:00Z',
    created_by: 'admin',
    created_by_name: 'Admin User'
  },
  {
    id: '4',
    title: 'New Lead Assignment',
    message: 'You have been assigned a new lead: Alice Johnson. Please follow up within 24 hours.',
    type: 'INFO',
    priority: 'HIGH',
    status: 'UNREAD',
    channels: ['IN_APP', 'EMAIL'],
    recipient_id: 'staff1',
    recipient_type: 'USER',
    recipient_name: 'Staff Member',
    category: 'LEAD',
    action_url: '/leads',
    action_text: 'View Lead',
    created_at: '2024-01-28T16:45:00Z',
    updated_at: '2024-01-28T16:45:00Z',
    created_by: 'reception',
    created_by_name: 'Reception Staff'
  },
  {
    id: '5',
    title: 'Payment Failed',
    message: 'Your payment attempt for $1200 has failed. Please check your payment method and try again.',
    type: 'ERROR',
    priority: 'URGENT',
    status: 'UNREAD',
    channels: ['IN_APP', 'EMAIL', 'SMS'],
    recipient_id: 'student3',
    recipient_type: 'STUDENT',
    recipient_name: 'Ahmed Hassan',
    category: 'PAYMENT',
    action_url: '/payments',
    action_text: 'Retry Payment',
    created_at: '2024-01-28T18:20:00Z',
    updated_at: '2024-01-28T18:20:00Z',
    created_by: 'system',
    created_by_name: 'Payment System'
  }
]

const mockStats: NotificationStats = {
  total_notifications: 1247,
  unread_notifications: 23,
  sent_today: 45,
  delivered_today: 42,
  read_rate: 78.5,
  click_rate: 23.4,
  notifications_by_type: {
    INFO: 456,
    SUCCESS: 234,
    WARNING: 123,
    ERROR: 89,
    REMINDER: 345
  },
  notifications_by_channel: {
    IN_APP: 1247,
    EMAIL: 892,
    SMS: 234,
    PUSH: 567
  }
}

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<NotificationFilters>({
    search: '',
    type: 'ALL',
    status: 'ALL',
    priority: 'ALL',
    channel: 'ALL',
    date_from: '',
    date_to: ''
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null)

  // Load notifications on component mount
  useEffect(() => {
    loadNotifications()
    loadStats()
  }, [])

  const loadNotifications = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Failed to load notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setStats(mockStats)
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch =
      notification.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      notification.message.toLowerCase().includes(filters.search.toLowerCase()) ||
      notification.recipient_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
      notification.category?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesType = filters.type === 'ALL' || notification.type === filters.type
    const matchesStatus = filters.status === 'ALL' || notification.status === filters.status
    const matchesPriority = filters.priority === 'ALL' || notification.priority === filters.priority
    const matchesChannel = filters.channel === 'ALL' || notification.channels.includes(filters.channel)

    let matchesDateRange = true
    if (filters.date_from && filters.date_to) {
      const notificationDate = new Date(notification.created_at)
      const fromDate = new Date(filters.date_from)
      const toDate = new Date(filters.date_to)
      matchesDateRange = notificationDate >= fromDate && notificationDate <= toDate
    }

    return matchesSearch && matchesType && matchesStatus && matchesPriority && matchesChannel && matchesDateRange
  })

  const handleCreateNotification = async (notificationData: NotificationCreateRequest) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const newNotification: Notification = {
        id: Date.now().toString(),
        ...notificationData,
        status: 'UNREAD',
        recipient_name: notificationData.recipient_id ? 'Recipient Name' : undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 'current_user',
        created_by_name: 'Current User'
      }

      setNotifications(prev => [newNotification, ...prev])
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create notification:', error)
    }
  }

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      setNotifications(prev => prev.map(notification =>
        notification.id === notificationId
          ? {
              ...notification,
              status: 'READ' as NotificationStatus,
              read_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          : notification
      ))
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setNotifications(prev => prev.map(notification =>
        notification.status === 'UNREAD'
          ? {
              ...notification,
              status: 'READ' as NotificationStatus,
              read_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          : notification
      ))
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  const handleDeleteNotification = async (notificationId: string) => {
    if (!confirm('Are you sure you want to delete this notification?')) return

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId))
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }

  const getTypeIcon = (type: NotificationType) => {
    const icons = {
      INFO: InformationCircleIcon,
      SUCCESS: CheckCircleIcon,
      WARNING: ExclamationTriangleIcon,
      ERROR: XCircleIcon,
      REMINDER: ClockIcon
    }
    return icons[type] || InformationCircleIcon
  }

  const getTypeBadgeColor = (type: NotificationType) => {
    const colors = {
      INFO: 'bg-blue-100 text-blue-800',
      SUCCESS: 'bg-green-100 text-green-800',
      WARNING: 'bg-yellow-100 text-yellow-800',
      ERROR: 'bg-red-100 text-red-800',
      REMINDER: 'bg-purple-100 text-purple-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const getPriorityBadgeColor = (priority: NotificationPriority) => {
    const colors = {
      LOW: 'bg-gray-100 text-gray-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      URGENT: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
  }

  const getChannelIcon = (channel: NotificationChannel) => {
    const icons = {
      IN_APP: BellIcon,
      EMAIL: EnvelopeIcon,
      SMS: DevicePhoneMobileIcon,
      PUSH: ComputerDesktopIcon
    }
    return icons[channel] || BellIcon
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`

    return formatDate(dateString)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          <p className="text-gray-600 mt-1">
            Manage email, SMS, and in-app notifications
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            onClick={handleMarkAllAsRead}
            className="flex items-center gap-2"
          >
            <CheckCircleIcon className="h-4 w-4" />
            Mark All Read
          </Button>
          <Button
            variant="secondary"
            className="flex items-center gap-2"
          >
            <Cog6ToothIcon className="h-4 w-4" />
            Settings
          </Button>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            Send Notification
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BellIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Notifications</p>
                <p className="text-xl font-semibold text-gray-900">{stats.total_notifications}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Unread</p>
                <p className="text-xl font-semibold text-gray-900">{stats.unread_notifications}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Sent Today</p>
                <p className="text-xl font-semibold text-gray-900">{stats.sent_today}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Read Rate</p>
                <p className="text-xl font-semibold text-gray-900">{stats.read_rate}%</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search notifications..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <select
            value={filters.type}
            onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value as NotificationType | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Types</option>
            <option value="INFO">Info</option>
            <option value="SUCCESS">Success</option>
            <option value="WARNING">Warning</option>
            <option value="ERROR">Error</option>
            <option value="REMINDER">Reminder</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as NotificationStatus | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Status</option>
            <option value="UNREAD">Unread</option>
            <option value="READ">Read</option>
            <option value="ARCHIVED">Archived</option>
          </select>

          <select
            value={filters.priority}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value as NotificationPriority | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Priorities</option>
            <option value="LOW">Low</option>
            <option value="MEDIUM">Medium</option>
            <option value="HIGH">High</option>
            <option value="URGENT">Urgent</option>
          </select>

          <select
            value={filters.channel}
            onChange={(e) => setFilters(prev => ({ ...prev, channel: e.target.value as NotificationChannel | 'ALL' }))}
            className="input"
          >
            <option value="ALL">All Channels</option>
            <option value="IN_APP">In-App</option>
            <option value="EMAIL">Email</option>
            <option value="SMS">SMS</option>
            <option value="PUSH">Push</option>
          </select>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4" />
            {filteredNotifications.length} of {notifications.length} notifications
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Notifications</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredNotifications.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              No notifications found matching your criteria
            </div>
          ) : (
            filteredNotifications.map((notification) => {
              const TypeIcon = getTypeIcon(notification.type)
              return (
                <div
                  key={notification.id}
                  className={`p-6 hover:bg-gray-50 transition-colors ${
                    notification.status === 'UNREAD' ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${
                      notification.type === 'ERROR' ? 'bg-red-100' :
                      notification.type === 'WARNING' ? 'bg-yellow-100' :
                      notification.type === 'SUCCESS' ? 'bg-green-100' :
                      notification.type === 'REMINDER' ? 'bg-purple-100' :
                      'bg-blue-100'
                    }`}>
                      <TypeIcon className={`h-5 w-5 ${
                        notification.type === 'ERROR' ? 'text-red-600' :
                        notification.type === 'WARNING' ? 'text-yellow-600' :
                        notification.type === 'SUCCESS' ? 'text-green-600' :
                        notification.type === 'REMINDER' ? 'text-purple-600' :
                        'text-blue-600'
                      }`} />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className={`text-sm font-medium ${
                              notification.status === 'UNREAD' ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </h3>
                            <Badge className={getTypeBadgeColor(notification.type)}>
                              {notification.type}
                            </Badge>
                            <Badge className={getPriorityBadgeColor(notification.priority)}>
                              {notification.priority}
                            </Badge>
                            {notification.status === 'UNREAD' && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>
                              To: {notification.recipient_name || notification.recipient_type.replace('_', ' ')}
                            </span>
                            <span>
                              {getRelativeTime(notification.created_at)}
                            </span>
                            <span>
                              By: {notification.created_by_name}
                            </span>
                            <div className="flex items-center gap-1">
                              {notification.channels.map((channel, index) => {
                                const ChannelIcon = getChannelIcon(channel)
                                return (
                                  <ChannelIcon key={index} className="h-3 w-3" />
                                )
                              })}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          {notification.action_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-700"
                            >
                              {notification.action_text || 'View'}
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedNotification(notification)
                              setShowViewModal(true)
                            }}
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Button>
                          {notification.status === 'UNREAD' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleMarkAsRead(notification.id)}
                              title="Mark as read"
                            >
                              <CheckCircleIcon className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteNotification(notification.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </div>
      {/* Modals */}
      {showCreateModal && (
        <NotificationCreateModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateNotification}
        />
      )}

      {showViewModal && selectedNotification && (
        <NotificationViewModal
          notification={selectedNotification}
          onClose={() => {
            setShowViewModal(false)
            setSelectedNotification(null)
          }}
        />
      )}
    </div>
  )
}

// Modal Components (placeholder implementations)
const NotificationCreateModal = ({ onClose, onSubmit }: {
  onClose: () => void
  onSubmit: (data: NotificationCreateRequest) => Promise<void>
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Send New Notification</h2>
        <p className="text-gray-600 mb-4">Notification creation form will be implemented here.</p>
        <div className="flex gap-2">
          <Button variant="secondary" onClick={onClose}>Cancel</Button>
          <Button onClick={onClose}>Send Notification</Button>
        </div>
      </div>
    </div>
  )
}

const NotificationViewModal = ({ notification, onClose }: {
  notification: Notification
  onClose: () => void
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getChannelIcon = (channel: NotificationChannel) => {
    const icons = {
      IN_APP: BellIcon,
      EMAIL: EnvelopeIcon,
      SMS: DevicePhoneMobileIcon,
      PUSH: ComputerDesktopIcon
    }
    return icons[channel] || BellIcon
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg font-semibold mb-4">Notification Details</h2>
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p><strong>Title:</strong> {notification.title}</p>
                <p><strong>Type:</strong> {notification.type}</p>
                <p><strong>Priority:</strong> {notification.priority}</p>
                <p><strong>Status:</strong> {notification.status}</p>
              </div>
              <div>
                <p><strong>Category:</strong> {notification.category || 'None'}</p>
                <p><strong>Created By:</strong> {notification.created_by_name}</p>
                <p><strong>Created:</strong> {formatDate(notification.created_at)}</p>
                {notification.expires_at && (
                  <p><strong>Expires:</strong> {formatDate(notification.expires_at)}</p>
                )}
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">Message</h3>
            <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">
              {notification.message}
            </p>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">Recipient</h3>
            <p><strong>Type:</strong> {notification.recipient_type.replace('_', ' ')}</p>
            {notification.recipient_name && (
              <p><strong>Name:</strong> {notification.recipient_name}</p>
            )}
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">Delivery Channels</h3>
            <div className="flex gap-2">
              {notification.channels.map((channel, index) => {
                const ChannelIcon = getChannelIcon(channel)
                return (
                  <div key={index} className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded">
                    <ChannelIcon className="h-4 w-4" />
                    <span className="text-xs">{channel.replace('_', ' ')}</span>
                  </div>
                )
              })}
            </div>
          </div>

          {(notification.sent_at || notification.delivered_at || notification.read_at) && (
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Delivery Status</h3>
              <div className="space-y-1">
                {notification.sent_at && (
                  <p><strong>Sent:</strong> {formatDate(notification.sent_at)}</p>
                )}
                {notification.delivered_at && (
                  <p><strong>Delivered:</strong> {formatDate(notification.delivered_at)}</p>
                )}
                {notification.read_at && (
                  <p><strong>Read:</strong> {formatDate(notification.read_at)}</p>
                )}
                {notification.clicked_at && (
                  <p><strong>Clicked:</strong> {formatDate(notification.clicked_at)}</p>
                )}
              </div>
            </div>
          )}

          {notification.action_url && (
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Action</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => window.open(notification.action_url, '_blank')}
                >
                  {notification.action_text || 'View'}
                </Button>
                <span className="text-xs text-gray-500">{notification.action_url}</span>
              </div>
            </div>
          )}
        </div>
        <div className="mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </div>
    </div>
  )
}

export default NotificationsPage
