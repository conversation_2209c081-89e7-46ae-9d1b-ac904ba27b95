#!/usr/bin/env node

/**
 * Test script to verify the frontend build is working correctly
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing React Frontend Build...\n');

// Test 1: Check if dist folder exists
console.log('1️⃣ Checking build output...');
const distPath = path.join(__dirname, 'dist');
if (fs.existsSync(distPath)) {
  console.log('✅ dist/ folder exists');
  
  // Check for key files
  const indexHtml = path.join(distPath, 'index.html');
  const assetsDir = path.join(distPath, 'assets');
  
  if (fs.existsSync(indexHtml)) {
    console.log('✅ index.html exists');
  } else {
    console.log('❌ index.html missing');
  }
  
  if (fs.existsSync(assetsDir)) {
    const assets = fs.readdirSync(assetsDir);
    console.log(`✅ assets/ folder exists with ${assets.length} files`);
    
    // Check for CSS and JS files
    const cssFiles = assets.filter(file => file.endsWith('.css'));
    const jsFiles = assets.filter(file => file.endsWith('.js'));
    
    console.log(`   - CSS files: ${cssFiles.length}`);
    console.log(`   - JS files: ${jsFiles.length}`);
  } else {
    console.log('❌ assets/ folder missing');
  }
} else {
  console.log('❌ dist/ folder not found - run npm run build first');
}

// Test 2: Check package.json
console.log('\n2️⃣ Checking package.json...');
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  console.log('✅ package.json exists');
  console.log(`   - Name: ${packageJson.name}`);
  console.log(`   - Version: ${packageJson.version}`);
  console.log(`   - Dependencies: ${Object.keys(packageJson.dependencies || {}).length}`);
  console.log(`   - Dev Dependencies: ${Object.keys(packageJson.devDependencies || {}).length}`);
} else {
  console.log('❌ package.json not found');
}

// Test 3: Check TypeScript config
console.log('\n3️⃣ Checking TypeScript configuration...');
const tsconfigPath = path.join(__dirname, 'tsconfig.json');
if (fs.existsSync(tsconfigPath)) {
  console.log('✅ tsconfig.json exists');
} else {
  console.log('❌ tsconfig.json not found');
}

// Test 4: Check Vite config
console.log('\n4️⃣ Checking Vite configuration...');
const viteConfigPath = path.join(__dirname, 'vite.config.ts');
if (fs.existsSync(viteConfigPath)) {
  console.log('✅ vite.config.ts exists');
} else {
  console.log('❌ vite.config.ts not found');
}

// Test 5: Check Tailwind config
console.log('\n5️⃣ Checking Tailwind configuration...');
const tailwindConfigPath = path.join(__dirname, 'tailwind.config.js');
if (fs.existsSync(tailwindConfigPath)) {
  console.log('✅ tailwind.config.js exists');
} else {
  console.log('❌ tailwind.config.js not found');
}

// Test 6: Check source structure
console.log('\n6️⃣ Checking source structure...');
const srcPath = path.join(__dirname, 'src');
if (fs.existsSync(srcPath)) {
  console.log('✅ src/ folder exists');
  
  const requiredFolders = ['components', 'pages', 'services', 'store', 'types', 'utils'];
  requiredFolders.forEach(folder => {
    const folderPath = path.join(srcPath, folder);
    if (fs.existsSync(folderPath)) {
      console.log(`   ✅ ${folder}/ exists`);
    } else {
      console.log(`   ❌ ${folder}/ missing`);
    }
  });
  
  // Check for key files
  const keyFiles = ['main.tsx', 'App.tsx', 'index.css', 'vite-env.d.ts'];
  keyFiles.forEach(file => {
    const filePath = path.join(srcPath, file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file} exists`);
    } else {
      console.log(`   ❌ ${file} missing`);
    }
  });
} else {
  console.log('❌ src/ folder not found');
}

console.log('\n🎉 Frontend build test completed!');
console.log('\n📝 Next steps:');
console.log('   1. Run: npm run dev (for development)');
console.log('   2. Run: npm run preview (to test production build)');
console.log('   3. Open: http://localhost:3000 or http://localhost:4173');
console.log('   4. Login with: <EMAIL> / admin123');
console.log('\n🚀 Ready for deployment!');
