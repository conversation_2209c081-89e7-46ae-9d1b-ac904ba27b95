const https = require('https');

console.log('🔍 Testing All Backend Services...\n');

const services = [
  { name: 'Auth Service', url: 'crm-auth-service.onrender.com' },
  { name: 'Admin Service', url: 'crm-admin-service.onrender.com' },
  { name: 'Staff Service', url: 'crm-staff-service.onrender.com' },
  { name: 'API Gateway', url: 'crm-api-gateway.onrender.com' }
];

async function testService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: service.url,
      port: 443,
      path: '/health',
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          name: service.name,
          status: res.statusCode,
          response: data,
          success: res.statusCode === 200
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: service.name,
        status: 'ERROR',
        response: error.message,
        success: false
      });
    });

    req.on('timeout', () => {
      resolve({
        name: service.name,
        status: 'TIMEOUT',
        response: 'Request timeout',
        success: false
      });
      req.destroy();
    });

    req.end();
  });
}

async function testAllServices() {
  console.log('Testing service health endpoints...\n');
  
  for (const service of services) {
    console.log(`Testing ${service.name}...`);
    const result = await testService(service);
    
    if (result.success) {
      console.log(`✅ ${result.name}: Status ${result.status}`);
      try {
        const parsed = JSON.parse(result.response);
        if (parsed.database) {
          console.log(`   Database: ${parsed.database.status} (${parsed.database.connections} connections)`);
        }
      } catch (e) {
        console.log(`   Response: ${result.response.substring(0, 100)}...`);
      }
    } else {
      console.log(`❌ ${result.name}: ${result.status} - ${result.response.substring(0, 100)}`);
    }
    console.log('');
  }
}

testAllServices().catch(console.error);
