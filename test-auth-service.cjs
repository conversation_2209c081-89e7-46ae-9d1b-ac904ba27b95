#!/usr/bin/env node

/**
 * Test script to check auth service endpoints
 */

const https = require('https');

console.log('🔍 Testing Auth Service Endpoints...\n');

// Test health endpoint
function testHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'crm-auth-service.onrender.com',
      port: 443,
      path: '/health',
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test login endpoint
function testLogin() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'admin123'
    });

    const options = {
      hostname: 'crm-auth-service.onrender.com',
      port: 443,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// Test different login endpoint paths
function testLoginPaths() {
  const paths = [
    '/api/v1/auth/login',
    '/auth/login',
    '/login',
    '/api/auth/login'
  ];

  return Promise.all(paths.map(path => {
    return new Promise((resolve) => {
      const postData = JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      });

      const options = {
        hostname: 'crm-auth-service.onrender.com',
        port: 443,
        path: path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        },
        timeout: 5000
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            path: path,
            status: res.statusCode,
            data: data.substring(0, 200) // Limit output
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          path: path,
          status: 'ERROR',
          data: error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          path: path,
          status: 'TIMEOUT',
          data: 'Request timeout'
        });
      });

      req.write(postData);
      req.end();
    });
  }));
}

async function runTests() {
  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResult = await testHealth();
    console.log(`   Status: ${healthResult.status}`);
    console.log(`   Response: ${healthResult.data.substring(0, 100)}...`);
    
    // Test 2: Login endpoint
    console.log('\n2️⃣ Testing login endpoint...');
    try {
      const loginResult = await testLogin();
      console.log(`   Status: ${loginResult.status}`);
      console.log(`   Headers: ${JSON.stringify(loginResult.headers)}`);
      console.log(`   Response: ${loginResult.data.substring(0, 200)}...`);
    } catch (error) {
      console.log(`   Error: ${error.message}`);
    }

    // Test 3: Try different paths
    console.log('\n3️⃣ Testing different login paths...');
    const pathResults = await testLoginPaths();
    pathResults.forEach(result => {
      console.log(`   ${result.path}: ${result.status} - ${result.data.substring(0, 50)}...`);
    });

  } catch (error) {
    console.error('Test failed:', error);
  }
}

runTests();
