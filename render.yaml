# Render Blueprint for Go Docker Platform
# Defines all services and infrastructure for production deployment

services:
  # Redis Cache
  - type: pserv
    name: crm-redis
    runtime: image
    image:
      url: redis:7-alpine
    disk:
      name: redis-data
      mountPath: /data
      sizeGB: 5

  # Auth Service
  - type: web
    name: crm-auth-service
    runtime: docker
    dockerfilePath: ./services/auth-service/Dockerfile
    dockerContext: .
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: "false"
      - key: DATABASE_URL
        value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # Override individual DB environment variables to prevent fallback
      - key: DB_HOST
        value: ""
      - key: DB_PORT
        value: ""
      - key: DB_USER
        value: ""
      - key: DB_PASSWORD
        value: ""
      - key: DB_NAME
        value: ""
      - key: DB_SSL_MODE
        value: ""
      - key: REDIS_HOST
        fromService:
          type: pserv
          name: crm-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: pserv
          name: crm-redis
          property: port
      - key: JWT_SECRET
        value: "crm-production-jwt-secret-2024-secure-key-v1-a8f9c2e1d4b7"
      - key: JWT_ACCESS_EXPIRY
        value: 15m
      - key: JWT_REFRESH_EXPIRY
        value: 7d
      - key: AUTH_SERVICE_PORT
        value: "8081"
      - key: LOG_LEVEL
        value: info
    healthCheckPath: /health

  # Admin Service
  - type: web
    name: crm-admin-service
    runtime: docker
    dockerfilePath: ./services/admin-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: "8082"
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # Override individual DB environment variables to prevent fallback
      - key: DB_HOST
        value: ""
      - key: DB_PORT
        value: ""
      - key: DB_USER
        value: ""
      - key: DB_PASSWORD
        value: ""
      - key: DB_NAME
        value: ""
      - key: DB_SSL_MODE
        value: ""
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
    healthCheckPath: /health

  # Staff Service
  - type: web
    name: crm-staff-service
    runtime: docker
    dockerfilePath: ./services/staff-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: "8083"
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # Override individual DB environment variables to prevent fallback
      - key: DB_HOST
        value: ""
      - key: DB_PORT
        value: ""
      - key: DB_USER
        value: ""
      - key: DB_PASSWORD
        value: ""
      - key: DB_NAME_STAFF
        value: ""
      - key: DB_SSL_MODE
        value: ""
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
    healthCheckPath: /health

  # Payment Service
  - type: web
    name: crm-payment-service
    runtime: docker
    dockerfilePath: ./services/payment-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: "8084"
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        value: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
      # Override individual DB environment variables to prevent fallback
      - key: DB_HOST
        value: ""
      - key: DB_PORT
        value: ""
      - key: DB_USER
        value: ""
      - key: DB_PASSWORD
        value: ""
      - key: DB_NAME
        value: ""
      - key: DB_SSL_MODE
        value: ""
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
      - key: ADMIN_SERVICE_URL
        value: https://crm-admin-service.onrender.com
      - key: STRIPE_SECRET_KEY
        generateValue: true
      - key: STRIPE_PUBLISHABLE_KEY
        generateValue: true
      - key: STRIPE_WEBHOOK_SECRET
        generateValue: true
      - key: PAYPAL_CLIENT_ID
        generateValue: true
      - key: PAYPAL_CLIENT_SECRET
        generateValue: true
      - key: PAYPAL_MODE
        value: live
      - key: DEFAULT_CURRENCY
        value: USD
      - key: MAX_PAYMENT_AMOUNT
        value: "100000.00"
      - key: PAYMENT_TIMEOUT_MINUTES
        value: "30"
      - key: REFUND_TIMEOUT_DAYS
        value: "30"
    healthCheckPath: /health

  # Notification Service
  - type: web
    name: crm-notification-service
    runtime: docker
    dockerfilePath: ./services/notification-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: "8085"
      - key: ENVIRONMENT
        value: production
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
      - key: SMTP_HOST
        value: smtp.gmail.com
      - key: SMTP_PORT
        value: "587"
      - key: SMTP_USERNAME
        generateValue: true
      - key: SMTP_PASSWORD
        generateValue: true
      - key: TWILIO_ACCOUNT_SID
        generateValue: true
      - key: TWILIO_AUTH_TOKEN
        generateValue: true
    healthCheckPath: /health

  # API Gateway
  - type: web
    name: crm-api-gateway
    runtime: docker
    dockerfilePath: ./services/api-gateway/Dockerfile
    dockerContext: .
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: "false"
      - key: API_GATEWAY_PORT
        value: "8080"
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
      - key: ADMIN_SERVICE_URL
        value: https://crm-admin-service.onrender.com
      - key: STAFF_SERVICE_URL
        value: https://crm-staff-service.onrender.com
      - key: PAYMENT_SERVICE_URL
        value: https://crm-payment-service.onrender.com
      - key: NOTIFICATION_SERVICE_URL
        value: https://crm-notification-service.onrender.com
      - key: CONFIG_VERSION
        value: "2025-07-07-v5-fixed-properties"
      - key: CORS_ORIGINS
        value: "https://crm-frontend.onrender.com,https://localhost:3000"
      - key: RATE_LIMIT_REQUESTS
        value: "1000"
      - key: RATE_LIMIT_WINDOW
        value: 1h
    healthCheckPath: /health

  # Frontend Service
  - type: web
    name: crm-frontend
    runtime: docker
    dockerfilePath: ./frontend/Dockerfile
    dockerContext: ./frontend
    envVars:
      - key: VITE_API_URL
        value: https://crm-api-gateway.onrender.com
      - key: VITE_API_VERSION
        value: v1
      - key: VITE_APP_NAME
        value: CRM Dashboard
      - key: VITE_APP_VERSION
        value: "1.0.0"
      - key: VITE_NODE_ENV
        value: production
    healthCheckPath: /
