# 🔧 CRM System Issue Resolution Plan

**Date**: January 15, 2025  
**Status**: Ready for Implementation  
**Priority**: CRITICAL - System not production ready

---

## 🎯 **Overview**

Based on comprehensive testing, we identified critical issues preventing the CRM system from functioning properly. This plan provides step-by-step instructions to resolve each issue.

---

## 🚨 **Critical Issues Summary**

1. **Port Configuration Conflicts**: Multiple services using wrong ports (CRITICAL)
2. **API Gateway**: Connection failures due to port issues (HIGH PRIORITY)
3. **Auth Service**: 500 Internal Server Errors (HIGH PRIORITY)
4. **Service URL Mismatches**: Services pointing to wrong URLs (HIGH PRIORITY)
5. **Environment Variables**: Missing or incorrect configurations (MEDIUM PRIORITY)

## 🔍 **Root Cause Analysis - Configuration Issues Found**

### **Port Conflicts Identified:**
- **Staff Service**: Config shows PORT=8080 but should be 8083
- **Notification Service**: Config shows PORT=8084 but should be 8085
- **Payment Service**: Config shows PORT=8083 (correct)
- **API Gateway**: Expects services on wrong ports

### **Service URL Mismatches:**
- **Docker Compose**: Staff service mapped to port 8084 (should be 8083)
- **Docker Compose**: Payment service mapped to port 8083 (should be 8084)
- **Frontend**: Hardcoded API Gateway URL may not match deployment

---

## 📋 **Step-by-Step Resolution Plan**

### **Phase 1: Service Status Investigation** 🔍

#### **1.1 Check Render Dashboard**
**What to do:**
1. Go to [Render Dashboard](https://dashboard.render.com)
2. Check status of all services:
   - `crm-frontend-k8b7` ✅ (Working)
   - `crm-api-gateway` ❌ (Issues)
   - `crm-auth-service` ⚠️ (Partial)
   - `crm-admin-service` ✅ (Working)
   - `crm-staff-service` ❌ (Timeout)
   - `crm-payment-service` ❌ (Timeout)
   - `crm-notification-service` ❌ (Timeout)

**Look for:**
- Service status (Running/Failed/Building)
- Recent deployment logs
- Error messages
- Resource usage

#### **1.2 Check Service Logs**
**For each failing service:**
1. Click on service name in Render dashboard
2. Go to "Logs" tab
3. Look for error messages, especially:
   - Database connection errors
   - Port binding issues
   - Environment variable problems
   - Startup failures

---

### **Phase 2: Fix API Gateway** 🌐

#### **2.1 Verify API Gateway Configuration**
**Check these files:**
```bash
# In your local repository
cd services/api-gateway
cat main.go  # Check port configuration
cat Dockerfile  # Verify build process
```

**Common issues to look for:**
- Wrong port binding (should be PORT from environment)
- Missing environment variables
- CORS configuration problems

#### **2.2 Redeploy API Gateway**
**Option A: Via Git Push**
```bash
git add .
git commit -m "Fix API Gateway configuration"
git push origin main
```

**Option B: Manual Redeploy**
1. Go to Render Dashboard → API Gateway service
2. Click "Manual Deploy" → "Deploy latest commit"

#### **2.3 Test API Gateway**
```powershell
# Test after redeployment
Invoke-WebRequest -Uri "https://crm-api-gateway.onrender.com/health" -UseBasicParsing
```

---

### **Phase 3: Fix Auth Service 500 Errors** 🔐

#### **3.1 Check Auth Service Logs**
**Look for these common issues:**
- Database connection failures
- Missing environment variables
- JWT secret configuration
- User table initialization

#### **3.2 Verify Database Connection**
**Test auth database directly:**
```bash
# Use the connection string from TESTING_GUIDE.md
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Check if users table exists and has data
\dt
SELECT * FROM users LIMIT 5;
```

#### **3.3 Check Environment Variables**
**Verify these are set in Render:**
- `DATABASE_URL`
- `JWT_SECRET`
- `PORT`
- `ENVIRONMENT`

#### **3.4 Test Admin User Creation**
**If users table is empty, create admin user:**
```sql
INSERT INTO users (id, email, username, password_hash, first_name, last_name, role, status, created_at, updated_at) 
VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'admin',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'System',
    'Administrator',
    'SUPER_ADMIN',
    'ACTIVE',
    NOW(),
    NOW()
);
```

---

### **Phase 4: Fix Service Timeouts** ⏱️

#### **4.1 Wake Up Services**
**Render free tier services sleep after inactivity. Wake them up:**
```powershell
# Hit each service endpoint to wake them up
Invoke-WebRequest -Uri "https://crm-staff-service.onrender.com/health" -TimeoutSec 30
Invoke-WebRequest -Uri "https://crm-payment-service.onrender.com/health" -TimeoutSec 30
Invoke-WebRequest -Uri "https://crm-notification-service.onrender.com/health" -TimeoutSec 30
```

#### **4.2 Check Service Dependencies**
**Verify each service can connect to its database:**
- Staff Service → ep-shy-fire database
- Payment Service → ep-floral-rice database  
- Notification Service → ep-tight-fog database

#### **4.3 Verify Service Configurations**
**Check these files for each service:**
```bash
cd services/staff-service
cat main.go  # Port and database configuration
cat .env.example  # Required environment variables

cd services/payment-service
cat main.go

cd services/notification-service  
cat main.go
```

---

### **Phase 5: Database Connectivity Testing** 💾

#### **5.1 Test All Database Connections**
```bash
# Auth Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"

# Admin Database  
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"

# Staff Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"

# Payment Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"

# Notification Database
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require" -c "SELECT 1;"
```

#### **5.2 Verify Required Tables Exist**
**For each database, check if tables are created:**
```sql
-- List all tables
\dt

-- Check specific tables based on service
-- Auth: users, user_sessions
-- Admin: admin_users, permissions  
-- Staff: students, courses, leads
-- Payment: payments, transactions
-- Notification: notifications, templates
```

---

### **Phase 6: Re-run Tests** ✅

#### **6.1 Execute Comprehensive Test Suite**
```powershell
# After all fixes, run the test commands from COMPREHENSIVE_TEST_EXECUTION.md
# Test each service health endpoint
# Test authentication flow
# Test frontend-backend communication
```

#### **6.2 Verify Full User Journey**
1. Frontend loads correctly
2. <NAME_EMAIL> / admin123
3. Dashboard displays properly
4. Navigation works
5. HTMX requests succeed

---

## 🛠️ **Tools and Resources**

### **Required Access:**
- Render Dashboard access
- GitHub repository access  
- Neon Database console access
- Local development environment

### **Useful Commands:**
```bash
# Local testing
cd frontend && go run main.go
cd services/auth-service && go run main.go

# Database access
psql "connection_string"

# Service testing
curl -X GET https://service-url/health
```

### **Documentation References:**
- `docs/TESTING_GUIDE.md` - Testing procedures
- `docs/DEPLOYMENT.md` - Deployment instructions
- `COMPREHENSIVE_TEST_EXECUTION.md` - Test results

---

## 📞 **Next Steps**

1. **Start with Phase 1** - Check Render service status
2. **Focus on API Gateway first** - It's the main entry point
3. **Fix Auth Service** - Critical for user access
4. **Address timeouts** - May resolve automatically after other fixes
5. **Test thoroughly** - Use the comprehensive test plan

**Estimated Time:** 2-4 hours depending on issues found

---

**🎯 Ready to begin? Start with Phase 1 and work through each step systematically!**
