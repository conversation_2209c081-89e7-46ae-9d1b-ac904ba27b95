# Render Blueprint for Go Docker Platform
# Defines all services and infrastructure for production deployment

services:
  # PostgreSQL Databases
  - type: pserv
    name: crm-postgres-auth
    env: docker
    dockerfilePath: ./deployments/render/postgres/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: crm_auth
      - key: POSTGRES_USER
        value: crm_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: postgres-auth-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  - type: pserv
    name: crm-postgres-admin
    env: docker
    dockerfilePath: ./deployments/render/postgres/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: crm_admin
      - key: POSTGRES_USER
        value: crm_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: postgres-admin-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  - type: pserv
    name: crm-postgres-staff
    env: docker
    dockerfilePath: ./deployments/render/postgres/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: crm_staff
      - key: POSTGRES_USER
        value: crm_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: postgres-staff-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  - type: pserv
    name: crm-postgres-payment
    env: docker
    dockerfilePath: ./deployments/render/postgres/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: crm_payment
      - key: POSTGRES_USER
        value: crm_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: postgres-payment-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  # Redis Cache
  - type: pserv
    name: crm-redis
    env: docker
    dockerfilePath: ./deployments/render/redis/Dockerfile
    disk:
      name: redis-data
      mountPath: /data
      sizeGB: 5

  # Auth Service
  - type: web
    name: crm-auth-service
    env: docker
    dockerfilePath: ./services/auth-service/Dockerfile
    dockerContext: ./services/auth-service
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: DB_HOST
        fromService:
          type: pserv
          name: crm-postgres-auth
          property: host
      - key: DB_PORT
        fromService:
          type: pserv
          name: crm-postgres-auth
          property: port
      - key: DB_USER
        fromService:
          type: pserv
          name: crm-postgres-auth
          envVarKey: POSTGRES_USER
      - key: DB_PASSWORD
        fromService:
          type: pserv
          name: crm-postgres-auth
          envVarKey: POSTGRES_PASSWORD
      - key: DB_NAME_AUTH
        value: crm_auth
      - key: DB_SSL_MODE
        value: require
      - key: REDIS_HOST
        fromService:
          type: pserv
          name: crm-redis
          property: host
      - key: REDIS_PORT
        fromService:
          type: pserv
          name: crm-redis
          property: port
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_ACCESS_EXPIRY
        value: 15m
      - key: JWT_REFRESH_EXPIRY
        value: 7d
      - key: AUTH_SERVICE_PORT
        value: 8081
      - key: LOG_LEVEL
        value: info
    healthCheckPath: /health

  # Admin Service
  - type: web
    name: crm-admin-service
    env: docker
    dockerfilePath: ./services/admin-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: 8082
      - key: ENVIRONMENT
        value: production
      - key: DB_HOST
        fromService:
          type: pserv
          name: crm-postgres-admin
          property: host
      - key: DB_PORT
        fromService:
          type: pserv
          name: crm-postgres-admin
          property: port
      - key: DB_USER
        fromService:
          type: pserv
          name: crm-postgres-admin
          envVarKey: POSTGRES_USER
      - key: DB_PASSWORD
        fromService:
          type: pserv
          name: crm-postgres-admin
          envVarKey: POSTGRES_PASSWORD
      - key: DB_NAME_ADMIN
        value: crm_admin
      - key: DB_SSL_MODE
        value: require
      - key: JWT_SECRET
        sync: false
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        fromService:
          type: web
          name: crm-auth-service
          property: host
    healthCheckPath: /health

  # Staff Service
  - type: web
    name: crm-staff-service
    env: docker
    dockerfilePath: ./services/staff-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: 8083
      - key: ENVIRONMENT
        value: production
      - key: DB_HOST
        fromService:
          type: pserv
          name: crm-postgres-staff
          property: host
      - key: DB_PORT
        fromService:
          type: pserv
          name: crm-postgres-staff
          property: port
      - key: DB_USER
        fromService:
          type: pserv
          name: crm-postgres-staff
          envVarKey: POSTGRES_USER
      - key: DB_PASSWORD
        fromService:
          type: pserv
          name: crm-postgres-staff
          envVarKey: POSTGRES_PASSWORD
      - key: DB_NAME_STAFF
        value: crm_staff
      - key: DB_SSL_MODE
        value: require
      - key: JWT_SECRET
        sync: false
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        fromService:
          type: web
          name: crm-auth-service
          property: host
    healthCheckPath: /health

  # Payment Service
  - type: web
    name: crm-payment-service
    env: docker
    dockerfilePath: ./services/payment-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: 8084
      - key: ENVIRONMENT
        value: production
      - key: DB_HOST
        fromService:
          type: pserv
          name: crm-postgres-payment
          property: host
      - key: DB_PORT
        fromService:
          type: pserv
          name: crm-postgres-payment
          property: port
      - key: DB_USER
        fromService:
          type: pserv
          name: crm-postgres-payment
          envVarKey: POSTGRES_USER
      - key: DB_PASSWORD
        fromService:
          type: pserv
          name: crm-postgres-payment
          envVarKey: POSTGRES_PASSWORD
      - key: DB_NAME_PAYMENT
        value: crm_payment
      - key: DB_SSL_MODE
        value: require
      - key: JWT_SECRET
        sync: false
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        fromService:
          type: web
          name: crm-auth-service
          property: host
      - key: ADMIN_SERVICE_URL
        fromService:
          type: web
          name: crm-admin-service
          property: host
      - key: STRIPE_SECRET_KEY
        generateValue: true
      - key: STRIPE_PUBLISHABLE_KEY
        generateValue: true
      - key: STRIPE_WEBHOOK_SECRET
        generateValue: true
      - key: PAYPAL_CLIENT_ID
        generateValue: true
      - key: PAYPAL_CLIENT_SECRET
        generateValue: true
      - key: PAYPAL_MODE
        value: live
      - key: DEFAULT_CURRENCY
        value: USD
      - key: MAX_PAYMENT_AMOUNT
        value: 100000.00
      - key: PAYMENT_TIMEOUT_MINUTES
        value: 30
      - key: REFUND_TIMEOUT_DAYS
        value: 30
    healthCheckPath: /health

  # Notification Service
  - type: web
    name: crm-notification-service
    env: docker
    dockerfilePath: ./services/notification-service/Dockerfile
    dockerContext: .
    envVars:
      - key: PORT
        value: 8085
      - key: ENVIRONMENT
        value: production
      - key: JWT_SECRET
        sync: false
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: REDIS_URL
        value: redis://crm-redis:6379
      - key: AUTH_SERVICE_URL
        fromService:
          type: web
          name: crm-auth-service
          property: host
      - key: SMTP_HOST
        value: smtp.gmail.com
      - key: SMTP_PORT
        value: 587
      - key: SMTP_USERNAME
        generateValue: true
      - key: SMTP_PASSWORD
        generateValue: true
      - key: TWILIO_ACCOUNT_SID
        generateValue: true
      - key: TWILIO_AUTH_TOKEN
        generateValue: true
    healthCheckPath: /health

  # API Gateway
  - type: web
    name: crm-api-gateway
    env: docker
    dockerfilePath: ./services/api-gateway/Dockerfile
    dockerContext: ./services/api-gateway
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: "false"
      - key: API_GATEWAY_PORT
        value: "8080"
      - key: JWT_SECRET
        fromService:
          type: web
          name: crm-auth-service
          envVarKey: JWT_SECRET
      - key: AUTH_SERVICE_URL
        value: https://crm-auth-service.onrender.com
      - key: ADMIN_SERVICE_URL
        value: https://crm-admin-service.onrender.com
      - key: STAFF_SERVICE_URL
        value: https://crm-staff-service.onrender.com
      - key: PAYMENT_SERVICE_URL
        value: https://crm-payment-service.onrender.com
      - key: NOTIFICATION_SERVICE_URL
        value: https://crm-notification-service.onrender.com
      - key: CONFIG_VERSION
        value: "2025-07-07-v3-fixed"
      - key: CORS_ORIGINS
        value: https://yourdomain.com,https://admin.yourdomain.com
      - key: RATE_LIMIT_REQUESTS
        value: "1000"
      - key: RATE_LIMIT_WINDOW
        value: 1h
    healthCheckPath: /health
