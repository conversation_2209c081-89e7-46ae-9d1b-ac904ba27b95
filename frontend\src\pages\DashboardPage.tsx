import { useAuth } from '@/store/auth'
import {
  UsersIcon,
  AcademicCapIcon,
  UserGroupIcon,
  CreditCardIcon,
  ChartBarIcon,
  BookOpenIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  DocumentChartBarIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const DashboardPage = () => {
  const { user } = useAuth()

  // Mock stats - in real app, these would come from API
  const stats = [
    {
      name: 'Total Students',
      value: '1,234',
      change: '+12%',
      changeType: 'positive',
      icon: AcademicCapIcon,
    },
    {
      name: 'Active Leads',
      value: '89',
      change: '+5%',
      changeType: 'positive',
      icon: UserGroupIcon,
    },
    {
      name: 'Active Courses',
      value: '24',
      change: '+2%',
      changeType: 'positive',
      icon: BookOpenIcon,
    },
    {
      name: 'Monthly Revenue',
      value: '$45,231',
      change: '+18%',
      changeType: 'positive',
      icon: CreditCardIcon,
    },
  ]

  // Enhanced analytics data
  const monthlyData = [
    { month: 'Jan', students: 120, revenue: 36000, leads: 45 },
    { month: 'Feb', students: 135, revenue: 40500, leads: 52 },
    { month: 'Mar', students: 142, revenue: 42600, leads: 48 },
    { month: 'Apr', students: 158, revenue: 47400, leads: 61 },
    { month: 'May', students: 167, revenue: 50100, leads: 58 },
    { month: 'Jun', students: 174, revenue: 52200, leads: 64 }
  ]

  const conversionMetrics = {
    leadToStudent: 23.5,
    studentRetention: 89.2,
    courseCompletion: 76.8,
    paymentSuccess: 94.1
  }

  const topCourses = [
    { name: 'Advanced Mathematics', students: 45, revenue: 54000 },
    { name: 'English Literature', students: 38, revenue: 38000 },
    { name: 'Computer Science', students: 42, revenue: 63000 },
    { name: 'Business Studies', students: 29, revenue: 29000 }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'student_enrolled',
      message: 'John Doe enrolled in Advanced English',
      time: '2 hours ago',
      icon: AcademicCapIcon,
      color: 'text-green-600'
    },
    {
      id: 2,
      type: 'payment_received',
      message: 'Payment received from Sarah Smith - $299',
      time: '4 hours ago',
      icon: CreditCardIcon,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'lead_converted',
      message: 'Lead Mike Johnson converted to student',
      time: '6 hours ago',
      icon: UserGroupIcon,
      color: 'text-purple-600'
    },
    {
      id: 4,
      type: 'course_created',
      message: 'New course "Business English" created',
      time: '1 day ago',
      icon: BookOpenIcon,
      color: 'text-orange-600'
    },
    {
      id: 5,
      type: 'payment_overdue',
      message: 'Payment overdue for Ahmed Hassan - $1200',
      time: '1 day ago',
      icon: CreditCardIcon,
      color: 'text-red-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.first_name}!
            </h1>
            <p className="text-gray-600">
              Here's what's happening with your CRM today.
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Role</p>
            <p className="text-lg font-semibold text-primary-600 capitalize">
              {user?.role?.toLowerCase().replace('_', ' ')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Analytics Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends Chart */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Monthly Trends</h3>
            <Button variant="ghost" size="sm">
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-semibold text-blue-600">174</p>
                <p className="text-sm text-gray-500">Students</p>
                <div className="flex items-center justify-center mt-1">
                  <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-xs text-green-500">+12%</span>
                </div>
              </div>
              <div>
                <p className="text-2xl font-semibold text-green-600">$52.2K</p>
                <p className="text-sm text-gray-500">Revenue</p>
                <div className="flex items-center justify-center mt-1">
                  <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-xs text-green-500">+18%</span>
                </div>
              </div>
              <div>
                <p className="text-2xl font-semibold text-purple-600">64</p>
                <p className="text-sm text-gray-500">Leads</p>
                <div className="flex items-center justify-center mt-1">
                  <TrendingUpIcon className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-xs text-green-500">+8%</span>
                </div>
              </div>
            </div>
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 text-center">
                📊 Interactive charts would be displayed here using Chart.js or Recharts
              </p>
            </div>
          </div>
        </Card>

        {/* Conversion Metrics */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Conversion Metrics</h3>
            <Button variant="ghost" size="sm">
              <DocumentChartBarIcon className="h-4 w-4 mr-2" />
              Details
            </Button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Lead to Student</span>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${conversionMetrics.leadToStudent}%` }}></div>
                </div>
                <span className="text-sm font-medium">{conversionMetrics.leadToStudent}%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Student Retention</span>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: `${conversionMetrics.studentRetention}%` }}></div>
                </div>
                <span className="text-sm font-medium">{conversionMetrics.studentRetention}%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Course Completion</span>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div className="bg-purple-600 h-2 rounded-full" style={{ width: `${conversionMetrics.courseCompletion}%` }}></div>
                </div>
                <span className="text-sm font-medium">{conversionMetrics.courseCompletion}%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Payment Success</span>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div className="bg-emerald-600 h-2 rounded-full" style={{ width: `${conversionMetrics.paymentSuccess}%` }}></div>
                </div>
                <span className="text-sm font-medium">{conversionMetrics.paymentSuccess}%</span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Top Performing Courses */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Top Performing Courses</h3>
          <Button variant="ghost" size="sm">
            View All
          </Button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 text-sm font-medium text-gray-500">Course</th>
                <th className="text-left py-2 text-sm font-medium text-gray-500">Students</th>
                <th className="text-left py-2 text-sm font-medium text-gray-500">Revenue</th>
                <th className="text-left py-2 text-sm font-medium text-gray-500">Performance</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {topCourses.map((course, index) => (
                <tr key={index}>
                  <td className="py-3 text-sm font-medium text-gray-900">{course.name}</td>
                  <td className="py-3 text-sm text-gray-600">{course.students}</td>
                  <td className="py-3 text-sm text-gray-600">${course.revenue.toLocaleString()}</td>
                  <td className="py-3">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(course.students / 50) * 100}%` }}
                        ></div>
                      </div>
                      <TrendingUpIcon className="h-4 w-4 text-green-500" />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
          </div>
          <div className="p-6">
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivities.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full bg-white flex items-center justify-center ring-8 ring-white border-2 ${
                            activity.color === 'text-green-600' ? 'border-green-200' :
                            activity.color === 'text-blue-600' ? 'border-blue-200' :
                            activity.color === 'text-purple-600' ? 'border-purple-200' :
                            activity.color === 'text-orange-600' ? 'border-orange-200' :
                            'border-red-200'
                          }`}>
                            <activity.icon className={`h-4 w-4 ${activity.color}`} />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-500">{activity.message}</p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-2 gap-4">
              {user?.role === 'ADMIN' || user?.role === 'CASHIER' ? (
                <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <UsersIcon className="h-8 w-8 text-primary-600 mb-2" />
                  <span className="text-sm font-medium">Add User</span>
                </button>
              ) : null}
              
              {['RECEPTION', 'TEACHER', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '') ? (
                <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <AcademicCapIcon className="h-8 w-8 text-primary-600 mb-2" />
                  <span className="text-sm font-medium">Add Student</span>
                </button>
              ) : null}
              
              {['RECEPTION', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '') ? (
                <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <UserGroupIcon className="h-8 w-8 text-primary-600 mb-2" />
                  <span className="text-sm font-medium">Add Lead</span>
                </button>
              ) : null}
              
              {['TEACHER', 'MANAGER', 'ACADEMIC_MANAGER', 'ADMIN'].includes(user?.role || '') ? (
                <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <BookOpenIcon className="h-8 w-8 text-primary-600 mb-2" />
                  <span className="text-sm font-medium">Add Course</span>
                </button>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
            <span className="text-sm text-gray-600">All Services Online</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
            <span className="text-sm text-gray-600">Database Connected</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
            <span className="text-sm text-gray-600">API Gateway Healthy</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
