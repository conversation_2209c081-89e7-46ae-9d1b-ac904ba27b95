# Production Environment Variables for Render Deployment
# These variables should be set in Render dashboard for each service

# Global Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info

# Database Configuration (PostgreSQL)
DB_SSL_MODE=require
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30s
DB_IDLE_TIMEOUT=10m

# Redis Configuration
REDIS_MAX_CONNECTIONS=10
REDIS_CONNECTION_TIMEOUT=5s
REDIS_IDLE_TIMEOUT=5m

# JWT Configuration
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_ALGORITHM=RS256

# Security Configuration
BCRYPT_COST=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_SPECIAL=true
PASSWORD_REQUIRE_NUMBER=true
PASSWORD_REQUIRE_UPPERCASE=true

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=1h
RATE_LIMIT_BURST=100

# CORS Configuration
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_MAX_AGE=86400

# Session Configuration
SESSION_TIMEOUT=30m
SESSION_CLEANUP_INTERVAL=1h

# File Upload Configuration
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30s

# Payment Configuration
DEFAULT_CURRENCY=USD
MAX_PAYMENT_AMOUNT=100000.00
PAYMENT_TIMEOUT_MINUTES=30
REFUND_TIMEOUT_DAYS=30
PAYPAL_MODE=live

# Notification Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_REQUIRE_TLS=true
EMAIL_RATE_LIMIT=100
SMS_RATE_LIMIT=50

# API Gateway Configuration
GATEWAY_TIMEOUT=30s
GATEWAY_RETRY_ATTEMPTS=3
GATEWAY_RETRY_DELAY=1s

# Service URLs (will be set automatically by Render)
# AUTH_SERVICE_URL=https://crm-auth-service.onrender.com
# ADMIN_SERVICE_URL=https://crm-admin-service.onrender.com
# STAFF_SERVICE_URL=https://crm-staff-service.onrender.com
# PAYMENT_SERVICE_URL=https://crm-payment-service.onrender.com
# NOTIFICATION_SERVICE_URL=https://crm-notification-service.onrender.com

# External Service Configuration (Set these in Render dashboard)
# STRIPE_SECRET_KEY=sk_live_...
# STRIPE_PUBLISHABLE_KEY=pk_live_...
# STRIPE_WEBHOOK_SECRET=whsec_...
# PAYPAL_CLIENT_ID=...
# PAYPAL_CLIENT_SECRET=...
# TWILIO_ACCOUNT_SID=...
# TWILIO_AUTH_TOKEN=...
# SMTP_USERNAME=...
# SMTP_PASSWORD=...

# Domain Configuration
# CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
# ALLOWED_HOSTS=yourdomain.com,admin.yourdomain.com

# Logging Configuration
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_ROTATION=daily
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=7

# Performance Configuration
GO_MAX_PROCS=0
GOMAXPROCS=0
GOMEMLIMIT=512MiB

# Health Check Configuration
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_TIMEOUT=10s
READINESS_CHECK_PATH=/ready
LIVENESS_CHECK_PATH=/live
