const https = require('https');

console.log('🔍 Testing Backend Services Directly...\n');

const services = [
  { name: 'Auth Service', hostname: 'crm-auth-service.onrender.com' },
  { name: 'Admin Service', hostname: 'crm-admin-service.onrender.com' },
  { name: 'Staff Service', hostname: 'crm-staff-service.onrender.com' },
  { name: 'Payment Service', hostname: 'crm-payment-service.onrender.com' },
  { name: 'Notification Service', hostname: 'crm-notification-service.onrender.com' }
];

function testService(service) {
  return new Promise((resolve) => {
    const options = {
      hostname: service.hostname,
      port: 443,
      path: '/health',
      method: 'GET',
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          name: service.name,
          hostname: service.hostname,
          status: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        name: service.name,
        hostname: service.hostname,
        status: 'ERROR',
        data: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: service.name,
        hostname: service.hostname,
        status: 'TIMEOUT',
        data: 'Request timeout'
      });
    });

    req.end();
  });
}

async function testAllBackendServices() {
  console.log('Testing individual backend services...\n');
  
  const results = [];
  
  for (const service of services) {
    console.log(`Testing ${service.name}...`);
    const result = await testService(service);
    results.push(result);
    
    if (result.status === 200) {
      console.log(`✅ ${result.name}: Status ${result.status}`);
      try {
        const parsed = JSON.parse(result.data);
        if (parsed.success !== undefined) {
          console.log(`   Success: ${parsed.success}`);
        }
        if (parsed.data && parsed.data.service) {
          console.log(`   Service: ${parsed.data.service}`);
        }
      } catch (e) {
        console.log(`   Response: ${result.data.substring(0, 100)}...`);
      }
    } else {
      console.log(`❌ ${result.name}: Status ${result.status}`);
      if (result.data.includes('suspended')) {
        console.log(`   🚨 SERVICE SUSPENDED`);
      } else if (result.status === 502) {
        console.log(`   🚨 BAD GATEWAY - Service may be down`);
      } else {
        console.log(`   Error: ${result.data.substring(0, 100)}...`);
      }
    }
    console.log('');
  }
  
  // Summary
  console.log('📊 SUMMARY:');
  const healthy = results.filter(r => r.status === 200).length;
  const total = results.length;
  console.log(`   Healthy services: ${healthy}/${total}`);
  
  const suspended = results.filter(r => r.data.includes('suspended')).length;
  if (suspended > 0) {
    console.log(`   🚨 Suspended services: ${suspended}`);
  }
  
  const badGateway = results.filter(r => r.status === 502).length;
  if (badGateway > 0) {
    console.log(`   🚨 Bad Gateway errors: ${badGateway}`);
  }
}

testAllBackendServices().catch(console.error);
